//@version=5
indicator("Trading Signal Bot - Advanced Market Structure", shorttitle="TSB-AMS", overlay=true, max_boxes_count=500, max_lines_count=500, max_labels_count=500)

// ============================================================================
// TRADING SIGNAL BOT - ADVANCED MARKET STRUCTURE INDICATOR
// Dựa trên TECHNICAL_SPECIFICATION.md đã được cải tiến
// Tích hợp Market Structure Analysis + Smart Money Concepts + Advanced Signals
// ============================================================================

// ============================================================================
// CÁC THAM SỐ CẤU HÌNH
// ============================================================================

// Market Structure Settings
group_ms = "🏗️ Market Structure Analysis"
enable_bos = input.bool(true, "Enable Break of Structure (BOS)", group=group_ms)
enable_choch = input.bool(true, "Enable Change of Character (CHoCH)", group=group_ms)
enable_ob = input.bool(true, "Enable Order Blocks", group=group_ms)
enable_fvg = input.bool(true, "Enable Fair Value Gaps", group=group_ms)
structure_lookback = input.int(20, "Structure Lookback Period", minval=5, maxval=100, group=group_ms)

// Smart Money Settings
group_sm = "💰 Smart Money Concepts"
enable_premium_discount = input.bool(true, "Enable Premium/Discount Zones", group=group_sm)
enable_liquidity = input.bool(true, "Enable Liquidity Zones", group=group_sm)
range_lookback = input.int(50, "Range Lookback for Premium/Discount", minval=20, maxval=200, group=group_sm)

// Technical Indicators Settings
group_ti = "📊 Technical Indicators"
ema_9 = input.int(9, "EMA 9", minval=1, group=group_ti)
ema_21 = input.int(21, "EMA 21", minval=1, group=group_ti)
ema_50 = input.int(50, "EMA 50", minval=1, group=group_ti)
ema_200 = input.int(200, "EMA 200", minval=1, group=group_ti)
rsi_length = input.int(14, "RSI Length", minval=1, group=group_ti)
macd_fast = input.int(12, "MACD Fast", minval=1, group=group_ti)
macd_slow = input.int(26, "MACD Slow", minval=1, group=group_ti)
macd_signal = input.int(9, "MACD Signal", minval=1, group=group_ti)
bb_length = input.int(20, "Bollinger Bands Length", minval=1, group=group_ti)
bb_mult = input.float(2.0, "Bollinger Bands Multiplier", minval=0.1, group=group_ti)
atr_length = input.int(14, "ATR Length", minval=1, group=group_ti)

// Signal Settings
group_sig = "🎯 Signal Generation"
min_confidence = input.int(75, "Minimum Confidence Score", minval=0, maxval=100, group=group_sig)
show_signals = input.bool(true, "Show Trading Signals", group=group_sig)
show_confidence = input.bool(true, "Show Confidence Score", group=group_sig)
risk_reward_min = input.float(2.0, "Minimum Risk:Reward Ratio", minval=1.0, group=group_sig)

// Visual Settings
group_vis = "🎨 Visual Settings"
ob_transparency = input.int(80, "Order Blocks Transparency", minval=0, maxval=100, group=group_vis)
fvg_transparency = input.int(70, "FVG Transparency", minval=0, maxval=100, group=group_vis)
zone_transparency = input.int(85, "Premium/Discount Transparency", minval=0, maxval=100, group=group_vis)

// ============================================================================
// TÍNH TOÁN CÁC CHỈ BÁO KỸ THUẬT
// ============================================================================

// Moving Averages
ema9 = ta.ema(close, ema_9)
ema21 = ta.ema(close, ema_21)
ema50 = ta.ema(close, ema_50)
ema200 = ta.ema(close, ema_200)

// RSI với Divergence Detection
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line and histogram > 0
macd_bearish = macd_line < signal_line and histogram < 0

// Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
bb_upper_break = close > bb_upper
bb_lower_break = close < bb_lower

// ATR và Volatility Classification
atr = ta.atr(atr_length)
atr_pct = (atr / close) * 100
volatility_low = atr_pct < 2
volatility_medium = atr_pct >= 2 and atr_pct < 4
volatility_high = atr_pct >= 4 and atr_pct < 6
volatility_extreme = atr_pct >= 6

// Volume Analysis
volume_avg = ta.sma(volume, 20)
volume_surge = volume > volume_avg * 2
volume_high = volume > volume_avg * 1.5

// ============================================================================
// MARKET STRUCTURE ANALYSIS
// ============================================================================

// Swing Highs và Lows Detection
swing_high = ta.pivothigh(high, structure_lookback, structure_lookback)
swing_low = ta.pivotlow(low, structure_lookback, structure_lookback)

// Break of Structure (BOS) Detection
var float last_swing_high = na
var float last_swing_low = na

if not na(swing_high)
    last_swing_high := swing_high

if not na(swing_low)
    last_swing_low := swing_low

// BOS Bullish: Price breaks above previous swing high
bos_bullish = not na(last_swing_high) and close > last_swing_high and close[1] <= last_swing_high
// BOS Bearish: Price breaks below previous swing low  
bos_bearish = not na(last_swing_low) and close < last_swing_low and close[1] >= last_swing_low

// Change of Character (CHoCH) Detection
var bool trend_bullish = true
choch_bearish = trend_bullish and bos_bearish
choch_bullish = not trend_bullish and bos_bullish

if choch_bullish
    trend_bullish := true
if choch_bearish
    trend_bullish := false

// ============================================================================
// ORDER BLOCKS DETECTION
// ============================================================================

// Bullish Order Block
if bos_bullish and enable_ob
    ob_candle_index = 0
    for i = 1 to 10
        if close[i] > open[i]
            ob_candle_index := i
            break
    
    if ob_candle_index > 0
        ob_top = high[ob_candle_index]
        ob_bottom = low[ob_candle_index]
        ob_left = bar_index - ob_candle_index
        ob_right = bar_index + 20
        
        box.new(ob_left, ob_top, ob_right, ob_bottom, border_color=color.new(color.green, 50), bgcolor=color.new(color.green, ob_transparency), text="OB Bull", text_color=color.green, text_size=size.small)

// Bearish Order Block
if bos_bearish and enable_ob
    ob_candle_index = 0
    for i = 1 to 10
        if close[i] < open[i]
            ob_candle_index := i
            break
    
    if ob_candle_index > 0
        ob_top = high[ob_candle_index]
        ob_bottom = low[ob_candle_index]
        ob_left = bar_index - ob_candle_index
        ob_right = bar_index + 20
        
        box.new(ob_left, ob_top, ob_right, ob_bottom, border_color=color.new(color.red, 50), bgcolor=color.new(color.red, ob_transparency), text="OB Bear", text_color=color.red, text_size=size.small)

// ============================================================================
// FAIR VALUE GAPS (FVG) DETECTION
// ============================================================================

// Bullish FVG: Gap giữa high[2] và low[0]
bullish_fvg = enable_fvg and low > high[2] and close > close[1] and close[1] > close[2]
// Bearish FVG: Gap giữa low[2] và high[0]
bearish_fvg = enable_fvg and high < low[2] and close < close[1] and close[1] < close[2]

if bullish_fvg
    fvg_top = low
    fvg_bottom = high[2]
    box.new(bar_index-2, fvg_top, bar_index+10, fvg_bottom, border_color=color.new(color.blue, 30), bgcolor=color.new(color.blue, fvg_transparency), text="FVG↑", text_color=color.blue, text_size=size.tiny)

if bearish_fvg
    fvg_top = low[2]
    fvg_bottom = high
    box.new(bar_index-2, fvg_top, bar_index+10, fvg_bottom, border_color=color.new(color.orange, 30), bgcolor=color.new(color.orange, fvg_transparency), text="FVG↓", text_color=color.orange, text_size=size.tiny)

// ============================================================================
// PREMIUM/DISCOUNT ZONES
// ============================================================================

// Tính toán range cao nhất và thấp nhất trong lookback period
range_high = ta.highest(high, range_lookback)
range_low = ta.lowest(low, range_lookback)
range_mid = (range_high + range_low) / 2

// Premium Zone: Trên 50% của range
premium_zone_bottom = range_mid
premium_zone_top = range_high

// Discount Zone: Dưới 50% của range  
discount_zone_top = range_mid
discount_zone_bottom = range_low

// Xác định vị trí hiện tại
in_premium = close > range_mid
in_discount = close < range_mid
in_equilibrium = math.abs(close - range_mid) / (range_high - range_low) < 0.1

// ============================================================================
// CONFIDENCE SCORE CALCULATION
// ============================================================================

// Tính toán confidence score dựa trên 7 factors từ specification
confidence_score = 30 // Base score

// 1. Market Structure Analysis (25 points)
structure_points = 0
if bos_bullish or bos_bearish
    structure_points += 15
if choch_bullish or choch_bearish  
    structure_points += 10
confidence_score += structure_points

// 2. Smart Money Confirmation (20 points)
smart_money_points = 0
if volume_surge
    smart_money_points += 10
if (bos_bullish and in_discount) or (bos_bearish and in_premium)
    smart_money_points += 10
confidence_score += smart_money_points

// 3. Multi-timeframe Alignment (15 points) - Simplified for single timeframe
alignment_points = 0
ema_bullish_alignment = ema9 > ema21 and ema21 > ema50 and ema50 > ema200
ema_bearish_alignment = ema9 < ema21 and ema21 < ema50 and ema50 < ema200
if ema_bullish_alignment or ema_bearish_alignment
    alignment_points += 12
confidence_score += alignment_points

// 4. Traditional Indicators (15 points)
indicator_points = 0
if macd_bullish or macd_bearish
    indicator_points += 5
if rsi_oversold or rsi_overbought
    indicator_points += 5
if bb_upper_break or bb_lower_break
    indicator_points += 5
confidence_score += indicator_points

// 5. Market Regime Suitability (10 points)
regime_points = 0
if not volatility_extreme
    regime_points += 8
confidence_score += regime_points

// 6. Volume & Flow Analysis (10 points)
volume_points = 0
if volume_high
    volume_points += 5
if volume_surge
    volume_points += 5
confidence_score += volume_points

// 7. Risk Environment Assessment (5 points)
risk_points = 0
if volatility_low or volatility_medium
    risk_points += 3
confidence_score += risk_points

// Giới hạn confidence score trong khoảng 0-100
confidence_score := math.max(0, math.min(100, confidence_score))

// ============================================================================
// SIGNAL GENERATION LOGIC
// ============================================================================

// Điều kiện LONG Signal
long_structure = bos_bullish or (trend_bullish and not choch_bearish)
long_smart_money = in_discount and volume_high
long_technical = ema_bullish_alignment and (rsi_oversold or macd_bullish)
long_entry_trigger = close > ema21 or bb_lower_break

long_signal = show_signals and confidence_score >= min_confidence and long_structure and long_smart_money and long_technical and long_entry_trigger

// Điều kiện SHORT Signal
short_structure = bos_bearish or (not trend_bullish and not choch_bullish)
short_smart_money = in_premium and volume_high
short_technical = ema_bearish_alignment and (rsi_overbought or macd_bearish)
short_entry_trigger = close < ema21 or bb_upper_break

short_signal = show_signals and confidence_score >= min_confidence and short_structure and short_smart_money and short_technical and short_entry_trigger

// ============================================================================
// RISK MANAGEMENT CALCULATIONS
// ============================================================================

// Entry Price
entry_price = close

// Stop Loss Calculation (ATR-based + Structure-based)
atr_multiplier = volatility_low ? 1.5 : volatility_medium ? 2.0 : volatility_high ? 2.5 : 3.0
atr_stop_distance = atr * atr_multiplier

// Long Stop Loss
long_stop_loss = entry_price - atr_stop_distance
if not na(last_swing_low) and last_swing_low < long_stop_loss
    long_stop_loss := last_swing_low * 0.999

// Short Stop Loss
short_stop_loss = entry_price + atr_stop_distance
if not na(last_swing_high) and last_swing_high > short_stop_loss
    short_stop_loss := last_swing_high * 1.001

// Take Profit Calculation (Multiple targets)
long_tp1 = entry_price + (entry_price - long_stop_loss) * 2.0
long_tp2 = entry_price + (entry_price - long_stop_loss) * 3.0
long_tp3 = entry_price + (entry_price - long_stop_loss) * 5.0

short_tp1 = entry_price - (short_stop_loss - entry_price) * 2.0
short_tp2 = entry_price - (short_stop_loss - entry_price) * 3.0
short_tp3 = entry_price - (short_stop_loss - entry_price) * 5.0

// Risk:Reward Ratio
long_rr_ratio = (long_tp1 - entry_price) / (entry_price - long_stop_loss)
short_rr_ratio = (entry_price - short_tp1) / (short_stop_loss - entry_price)

// Validate minimum R:R ratio
long_valid_rr = long_rr_ratio >= risk_reward_min
short_valid_rr = short_rr_ratio >= risk_reward_min

// Final signal validation
final_long_signal = long_signal and long_valid_rr
final_short_signal = short_signal and short_valid_rr

// ============================================================================
// VISUAL ELEMENTS - PLOTTING
// ============================================================================

// Plot EMAs
plot(ema9, "EMA 9", color=color.new(color.blue, 0), linewidth=1)
plot(ema21, "EMA 21", color=color.new(color.orange, 0), linewidth=2)
plot(ema50, "EMA 50", color=color.new(color.red, 0), linewidth=2)
plot(ema200, "EMA 200", color=color.new(color.purple, 0), linewidth=3)

// Plot Bollinger Bands
p1 = plot(bb_upper, "BB Upper", color=color.new(color.gray, 50))
p2 = plot(bb_lower, "BB Lower", color=color.new(color.gray, 50))
fill(p1, p2, color=color.new(color.gray, 95), title="BB Fill")

// Premium/Discount Zones
if enable_premium_discount
    box.new(bar_index-1, premium_zone_top, bar_index, premium_zone_bottom, bgcolor=color.new(color.red, zone_transparency), border_color=color.new(color.red, 70))
    box.new(bar_index-1, discount_zone_top, bar_index, discount_zone_bottom, bgcolor=color.new(color.green, zone_transparency), border_color=color.new(color.green, 70))

// Plot BOS/CHoCH markers
plotshape(bos_bullish and enable_bos, "BOS Bullish", shape.triangleup, location.belowbar, color.new(color.green, 0), size=size.small, text="BOS↑")
plotshape(bos_bearish and enable_bos, "BOS Bearish", shape.triangledown, location.abovebar, color.new(color.red, 0), size=size.small, text="BOS↓")

plotshape(choch_bullish and enable_choch, "CHoCH Bullish", shape.diamond, location.belowbar, color.new(color.lime, 0), size=size.tiny, text="CHoCH↑")
plotshape(choch_bearish and enable_choch, "CHoCH Bearish", shape.diamond, location.abovebar, color.new(color.maroon, 0), size=size.tiny, text="CHoCH↓")

// ============================================================================
// SIGNAL ARROWS VÀ LABELS
// ============================================================================

// Long Signal Arrow
if final_long_signal
    label.new(bar_index, low - atr, text="🟢 LONG\nEntry: " + str.tostring(entry_price, "#.####") + "\nSL: " + str.tostring(long_stop_loss, "#.####") + "\nTP1: " + str.tostring(long_tp1, "#.####") + "\nR:R: 1:" + str.tostring(long_rr_ratio, "#.#") + "\nConf: " + str.tostring(confidence_score) + "%", style=label.style_label_up, color=color.new(color.green, 10), textcolor=color.white, size=size.normal)

// Short Signal Arrow
if final_short_signal
    label.new(bar_index, high + atr, text="🔴 SHORT\nEntry: " + str.tostring(entry_price, "#.####") + "\nSL: " + str.tostring(short_stop_loss, "#.####") + "\nTP1: " + str.tostring(short_tp1, "#.####") + "\nR:R: 1:" + str.tostring(short_rr_ratio, "#.#") + "\nConf: " + str.tostring(confidence_score) + "%", style=label.style_label_down, color=color.new(color.red, 10), textcolor=color.white, size=size.normal)

// Confidence Score Display
if show_confidence and (final_long_signal or final_short_signal)
    conf_color = confidence_score >= 90 ? color.lime : confidence_score >= 80 ? color.green : confidence_score >= 75 ? color.yellow : color.orange
    label.new(bar_index, high + atr*2, text="📊 " + str.tostring(confidence_score) + "%", style=label.style_label_down, color=color.new(conf_color, 20), textcolor=color.white, size=size.small)

// ============================================================================
// ALERTS SETUP
// ============================================================================

// Alert conditions
alertcondition(final_long_signal, "Long Signal", "🟢 LONG Signal Generated - {{ticker}}")
alertcondition(final_short_signal, "Short Signal", "🔴 SHORT Signal Generated - {{ticker}}")
alertcondition(bos_bullish, "BOS Bullish", "📈 Bullish Break of Structure - {{ticker}}")
alertcondition(bos_bearish, "BOS Bearish", "📉 Bearish Break of Structure - {{ticker}}")
alertcondition(choch_bullish, "CHoCH Bullish", "🔄 Bullish Change of Character - {{ticker}}")
alertcondition(choch_bearish, "CHoCH Bearish", "🔄 Bearish Change of Character - {{ticker}}")

// ============================================================================
// TABLE HIỂN THỊ THÔNG TIN
// ============================================================================

// Tạo table hiển thị thông tin market structure và confidence
if barstate.islast and show_confidence
    var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.new(color.white, 80), border_width=1, border_color=color.gray)

    // Headers
    table.cell(info_table, 0, 0, "Metric", text_color=color.black, text_size=size.small, bgcolor=color.new(color.blue, 70))
    table.cell(info_table, 1, 0, "Value", text_color=color.black, text_size=size.small, bgcolor=color.new(color.blue, 70))

    // Market Structure
    table.cell(info_table, 0, 1, "Trend", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 1, trend_bullish ? "Bullish" : "Bearish", text_color=trend_bullish ? color.green : color.red, text_size=size.tiny)

    // Position in Range
    table.cell(info_table, 0, 2, "Zone", text_color=color.black, text_size=size.tiny)
    zone_text = in_premium ? "Premium" : in_discount ? "Discount" : "Equilibrium"
    zone_color = in_premium ? color.red : in_discount ? color.green : color.gray
    table.cell(info_table, 1, 2, zone_text, text_color=zone_color, text_size=size.tiny)

    // Volatility
    table.cell(info_table, 0, 3, "Volatility", text_color=color.black, text_size=size.tiny)
    vol_text = volatility_extreme ? "Extreme" : volatility_high ? "High" : volatility_medium ? "Medium" : "Low"
    vol_color = volatility_extreme ? color.red : volatility_high ? color.orange : volatility_medium ? color.yellow : color.green
    table.cell(info_table, 1, 3, vol_text, text_color=vol_color, text_size=size.tiny)

    // Volume
    table.cell(info_table, 0, 4, "Volume", text_color=color.black, text_size=size.tiny)
    vol_status = volume_surge ? "Surge" : volume_high ? "High" : "Normal"
    vol_status_color = volume_surge ? color.lime : volume_high ? color.green : color.gray
    table.cell(info_table, 1, 4, vol_status, text_color=vol_status_color, text_size=size.tiny)

    // EMA Alignment
    table.cell(info_table, 0, 5, "EMA Align", text_color=color.black, text_size=size.tiny)
    ema_status = ema_bullish_alignment ? "Bull" : ema_bearish_alignment ? "Bear" : "Mixed"
    ema_color = ema_bullish_alignment ? color.green : ema_bearish_alignment ? color.red : color.gray
    table.cell(info_table, 1, 5, ema_status, text_color=ema_color, text_size=size.tiny)

    // RSI Status
    table.cell(info_table, 0, 6, "RSI", text_color=color.black, text_size=size.tiny)
    rsi_status = rsi_overbought ? "OB" : rsi_oversold ? "OS" : "Normal"
    rsi_color = rsi_overbought ? color.red : rsi_oversold ? color.green : color.gray
    table.cell(info_table, 1, 6, rsi_status + "(" + str.tostring(rsi, "#") + ")", text_color=rsi_color, text_size=size.tiny)

    // Confidence Score
    table.cell(info_table, 0, 7, "Confidence", text_color=color.black, text_size=size.tiny)
    conf_color_table = confidence_score >= 90 ? color.lime : confidence_score >= 80 ? color.green : confidence_score >= 75 ? color.yellow : confidence_score >= 60 ? color.orange : color.red
    table.cell(info_table, 1, 7, str.tostring(confidence_score) + "%", text_color=conf_color_table, text_size=size.tiny)

// ============================================================================
// NOTES VÀ CREDITS
// ============================================================================

// Indicator Notes:
// 1. Chỉ hiển thị signals khi confidence score >= 75
// 2. Sử dụng ATR-based stop loss với structure confirmation
// 3. Multiple take profit targets với tỷ lệ 1:2, 1:3, 1:5
// 4. Market structure analysis dựa trên swing highs/lows
// 5. Smart money concepts với premium/discount zones
// 6. Volume surge detection cho institutional flow
// 7. Multi-factor confidence scoring system

// Credits: Trading Signal Bot - Advanced Market Structure Indicator
// Based on TECHNICAL_SPECIFICATION.md - Enhanced Strategy
// Compatible with USDT-M Futures and Spot markets
