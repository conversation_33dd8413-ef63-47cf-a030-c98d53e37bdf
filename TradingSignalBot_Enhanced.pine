//@version=5
indicator("Trading Signal Bot - Enhanced Strategy", shorttitle="TSB-Enhanced", overlay=true, max_boxes_count=10, max_lines_count=10, max_labels_count=50)

// ============================================================================
// TRADING SIGNAL BOT - ENHANCED STRATEGY VERSION
// Cải tiến độ chính xác và tần suất tín hiệu dựa trên phân tích chiến lược
// Tối ưu hóa cho crypto trading với win rate cao và signal frequency tốt
// ============================================================================

// ============================================================================
// CẤU HÌNH THAM SỐ ĐÃ ĐƯỢC TỐI ƯU HÓA
// ============================================================================

// Technical Indicators Settings (Optimized for 15m Scalping)
group_ti = "📊 Technical Indicators (15m Scalping Optimized)"
ema_fast = input.int(5, "EMA Fast", minval=3, maxval=8, group=group_ti, tooltip="Faster EMA for scalping entries")
ema_medium = input.int(13, "EMA Medium", minval=10, maxval=18, group=group_ti, tooltip="Medium EMA for trend confirmation")
ema_slow = input.int(34, "EMA Slow", minval=25, maxval=40, group=group_ti, tooltip="Slower EMA for major trend")
ema_trend = input.int(89, "EMA Trend", minval=70, maxval=120, group=group_ti, tooltip="Trend filter EMA")
rsi_length = input.int(9, "RSI Length", minval=7, maxval=14, group=group_ti, tooltip="Faster RSI for scalping")
rsi_oversold = input.int(25, "RSI Oversold Level", minval=20, maxval=35, group=group_ti, tooltip="More sensitive oversold level")
rsi_overbought = input.int(75, "RSI Overbought Level", minval=65, maxval=80, group=group_ti, tooltip="More sensitive overbought level")
macd_fast = input.int(8, "MACD Fast", minval=6, maxval=12, group=group_ti, tooltip="Faster MACD for scalping")
macd_slow = input.int(17, "MACD Slow", minval=15, maxval=22, group=group_ti, tooltip="Optimized MACD slow")
macd_signal = input.int(6, "MACD Signal", minval=5, maxval=9, group=group_ti, tooltip="Faster signal line")
bb_length = input.int(15, "Bollinger Bands Length", minval=12, maxval=20, group=group_ti, tooltip="Shorter BB for scalping")
bb_mult = input.float(1.8, "Bollinger Bands Multiplier", minval=1.5, maxval=2.2, group=group_ti, tooltip="Tighter BB bands")
atr_length = input.int(10, "ATR Length", minval=7, maxval=15, group=group_ti, tooltip="Shorter ATR for scalping")

// Enhanced Signal Settings (Scalping Optimized)
group_sig = "🎯 Scalping Signal Generation"
min_confidence = input.int(75, "Minimum Confidence Score", minval=65, maxval=90, group=group_sig, tooltip="Higher confidence for scalping accuracy")
signal_mode = input.string("Aggressive", "Signal Mode", options=["Conservative", "Balanced", "Aggressive"], group=group_sig, tooltip="Aggressive mode for scalping")
show_signals = input.bool(true, "Show Trading Signals", group=group_sig)
show_confidence = input.bool(true, "Show Confidence Score", group=group_sig)
risk_reward_min = input.float(1.6, "Minimum Risk:Reward Ratio", minval=1.3, maxval=2.5, group=group_sig, tooltip="Scalping R:R ratio")
structure_lookback = input.int(8, "Structure Lookback Period", minval=5, maxval=15, group=group_sig, tooltip="Shorter lookback for scalping")
range_lookback = input.int(25, "Range Lookback", minval=20, maxval=35, group=group_sig, tooltip="Shorter range for scalping")

// Scalping Specific Settings
group_scalp = "⚡ Scalping Optimization"
enable_scalping_mode = input.bool(true, "Enable Scalping Mode", group=group_scalp, tooltip="Optimizations for 15m scalping")
noise_reduction_strength = input.int(3, "Noise Reduction Strength", minval=1, maxval=5, group=group_scalp, tooltip="1=Low, 5=High noise filtering")
momentum_threshold = input.float(0.6, "Momentum Threshold", minval=0.3, maxval=1.0, group=group_scalp, tooltip="Minimum momentum for entry")
volume_spike_multiplier = input.float(1.8, "Volume Spike Multiplier", minval=1.3, maxval=2.5, group=group_scalp, tooltip="Volume confirmation strength")

// Advanced Features
group_adv = "🔬 Advanced Features"
enable_momentum_filter = input.bool(true, "Enable Momentum Filter", group=group_adv)
enable_volatility_adaptive = input.bool(true, "Enable Volatility Adaptive", group=group_adv)
enable_trend_strength = input.bool(true, "Enable Trend Strength Analysis", group=group_adv)
enable_alternative_signals = input.bool(true, "Enable Alternative Signal Types", group=group_adv)

// Visual Settings
group_vis = "🎨 Visual Settings"
signal_label_size = input.string("Normal", "Signal Label Size", options=["Small", "Normal", "Large"], group=group_vis)
show_ema_lines = input.bool(true, "Show EMA Lines", group=group_vis)
show_bb_lines = input.bool(false, "Show Bollinger Bands", group=group_vis)
show_all_signals = input.bool(false, "Show All Signals", group=group_vis, tooltip="Hiển thị tất cả signals hay chỉ signal mới nhất")
max_signals_display = input.int(3, "Max Signals Display", minval=1, maxval=10, group=group_vis, tooltip="Ít signals hơn cho scalping")
compact_labels = input.bool(true, "Compact Signal Labels", group=group_vis, tooltip="Labels gọn gàng cho scalping")

// Enhanced Accuracy Settings (Scalping Focused)
group_acc = "🎯 Scalping Accuracy Enhancement"
enable_confluence_filter = input.bool(true, "Enable Confluence Filter", group=group_acc, tooltip="Yêu cầu nhiều điều kiện xác nhận")
enable_false_signal_filter = input.bool(true, "Enable False Signal Filter", group=group_acc, tooltip="Lọc bỏ các tín hiệu có khả năng sai cao")
min_signal_gap = input.int(2, "Minimum Signal Gap (bars)", minval=1, maxval=8, group=group_acc, tooltip="Shorter gap for scalping")
enable_market_session_filter = input.bool(true, "Enable Market Session Filter", group=group_acc, tooltip="Chỉ giao dịch trong giờ thị trường tốt")
enable_trend_momentum_filter = input.bool(true, "Enable Trend Momentum Filter", group=group_acc, tooltip="Lọc theo momentum trend mạnh")
enable_price_action_filter = input.bool(true, "Enable Price Action Filter", group=group_acc, tooltip="Lọc theo price action patterns")
scalping_confluence_min = input.int(4, "Scalping Confluence Minimum", minval=3, maxval=6, group=group_acc, tooltip="Minimum confluence score for scalping")

// ============================================================================
// ENHANCED TECHNICAL INDICATORS
// ============================================================================

// Optimized Moving Averages
ema_f = ta.ema(close, ema_fast)
ema_m = ta.ema(close, ema_medium)
ema_s = ta.ema(close, ema_slow)
ema_t = ta.ema(close, ema_trend)

// Enhanced RSI with dynamic levels (Scalping Optimized)
rsi = ta.rsi(close, rsi_length)
rsi_momentum = ta.rsi(close, 5) // Very fast RSI for scalping momentum
rsi_micro = ta.rsi(close, 3)    // Micro RSI for immediate momentum
rsi_is_oversold = rsi < rsi_oversold
rsi_is_overbought = rsi > rsi_overbought
rsi_bullish_momentum = rsi_momentum > 55 and rsi_momentum[1] <= 55 // Higher threshold for scalping
rsi_bearish_momentum = rsi_momentum < 45 and rsi_momentum[1] >= 45 // Lower threshold for scalping

// Advanced RSI Patterns for Scalping
rsi_divergence_bull = rsi > rsi[1] and close < close[1] and rsi < 40
rsi_divergence_bear = rsi < rsi[1] and close > close[1] and rsi > 60
rsi_momentum_strong = math.abs(rsi_momentum - rsi_momentum[1]) > 8
rsi_scalping_zone = rsi > 30 and rsi < 70 // Avoid extreme zones for scalping

// Enhanced MACD with histogram analysis
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line
macd_bearish = macd_line < signal_line
macd_histogram_increasing = histogram > histogram[1]
macd_histogram_decreasing = histogram < histogram[1]
macd_strong_bullish = macd_bullish and macd_histogram_increasing and histogram > 0
macd_strong_bearish = macd_bearish and macd_histogram_decreasing and histogram < 0

// Enhanced Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
bb_expansion = (bb_upper - bb_lower) / bb_middle > 0.15
bb_upper_touch = high >= bb_upper * 0.999
bb_lower_touch = low <= bb_lower * 1.001
bb_middle_cross_up = close > bb_middle and close[1] <= bb_middle
bb_middle_cross_down = close < bb_middle and close[1] >= bb_middle

// Enhanced ATR and Volatility
atr = ta.atr(atr_length)
atr_pct = (atr / close) * 100
volatility_low = atr_pct < 1.5
volatility_medium = atr_pct >= 1.5 and atr_pct < 3.0
volatility_high = atr_pct >= 3.0 and atr_pct < 5.0
volatility_extreme = atr_pct >= 5.0

// Enhanced Volume Analysis (Scalping Optimized)
volume_sma = ta.sma(volume, 12) // Shorter period for scalping
volume_ema = ta.ema(volume, 6)  // Faster EMA for volume
volume_surge = volume > volume_sma * volume_spike_multiplier
volume_high = volume > volume_sma * 1.4
volume_above_average = volume > volume_ema
volume_trend_up = volume_ema > volume_ema[2] // Shorter lookback
volume_momentum = (volume - volume[1]) / volume[1] * 100
volume_acceleration = volume_momentum > volume_momentum[1]

// Advanced Volume Patterns for Scalping
volume_breakout = volume > volume_sma * 2.2 and volume[1] <= volume_sma * 1.5
volume_climax = volume > ta.highest(volume, 10) * 0.9
volume_drying_up = volume < volume_sma * 0.7 and volume[1] < volume_sma * 0.7

// ============================================================================
// ENHANCED MARKET STRUCTURE ANALYSIS
// ============================================================================

// Optimized Swing Detection
swing_high = ta.pivothigh(high, structure_lookback, structure_lookback)
swing_low = ta.pivotlow(low, structure_lookback, structure_lookback)

// Enhanced BOS Detection
var float last_swing_high = na
var float last_swing_low = na
var float prev_swing_high = na
var float prev_swing_low = na

if not na(swing_high)
    prev_swing_high := last_swing_high
    last_swing_high := swing_high

if not na(swing_low)
    prev_swing_low := last_swing_low
    last_swing_low := swing_low

// Multiple BOS confirmation levels
bos_bullish_strong = not na(last_swing_high) and close > last_swing_high and close[1] <= last_swing_high
bos_bearish_strong = not na(last_swing_low) and close < last_swing_low and close[1] >= last_swing_low
bos_bullish_weak = not na(prev_swing_high) and close > prev_swing_high and not bos_bullish_strong
bos_bearish_weak = not na(prev_swing_low) and close < prev_swing_low and not bos_bearish_strong

// Enhanced Trend Analysis
var bool trend_bullish = true
var int trend_strength = 0

// Trend strength calculation
if bos_bullish_strong
    trend_bullish := true
    trend_strength := math.min(trend_strength + 2, 5)
else if bos_bearish_strong
    trend_bullish := false
    trend_strength := math.max(trend_strength - 2, -5)
else if bos_bullish_weak
    trend_strength := math.min(trend_strength + 1, 5)
else if bos_bearish_weak
    trend_strength := math.max(trend_strength - 1, -5)

// Premium/Discount Zones with multiple timeframes
range_high = ta.highest(high, range_lookback)
range_low = ta.lowest(low, range_lookback)
range_mid = (range_high + range_low) / 2
range_quarter_upper = range_mid + (range_high - range_mid) * 0.5
range_quarter_lower = range_mid - (range_mid - range_low) * 0.5

in_premium = close > range_quarter_upper
in_discount = close < range_quarter_lower
in_equilibrium = close >= range_quarter_lower and close <= range_quarter_upper

// ============================================================================
// ENHANCED CONFIDENCE SCORING SYSTEM
// ============================================================================

// Base confidence score increased
confidence_score = 40 // Increased base score

// 1. Enhanced Market Structure Analysis (30 points)
structure_points = 0
if bos_bullish_strong or bos_bearish_strong
    structure_points += 20
else if bos_bullish_weak or bos_bearish_weak
    structure_points += 10

if trend_strength >= 3 or trend_strength <= -3
    structure_points += 10
else if trend_strength >= 2 or trend_strength <= -2
    structure_points += 5

confidence_score += structure_points

// 2. Enhanced Smart Money Confirmation (25 points)
smart_money_points = 0
if volume_surge
    smart_money_points += 15
else if volume_high
    smart_money_points += 10
else if volume_above_average
    smart_money_points += 5

if volume_trend_up
    smart_money_points += 5

if (trend_bullish and in_discount) or (not trend_bullish and in_premium)
    smart_money_points += 5

confidence_score += smart_money_points

// 3. Enhanced Technical Alignment (20 points)
technical_points = 0
ema_bullish_alignment = ema_f > ema_m and ema_m > ema_s
ema_bearish_alignment = ema_f < ema_m and ema_m < ema_s
ema_trend_bullish = ema_s > ema_t
ema_trend_bearish = ema_s < ema_t

if (ema_bullish_alignment and ema_trend_bullish) or (ema_bearish_alignment and ema_trend_bearish)
    technical_points += 15
else if ema_bullish_alignment or ema_bearish_alignment
    technical_points += 10

if enable_trend_strength
    price_above_ema_f = close > ema_f
    price_below_ema_f = close < ema_f
    if (trend_bullish and price_above_ema_f) or (not trend_bullish and price_below_ema_f)
        technical_points += 5

confidence_score += technical_points

// 4. Enhanced Momentum Indicators (15 points)
momentum_points = 0
if macd_strong_bullish or macd_strong_bearish
    momentum_points += 8
else if macd_bullish or macd_bearish
    momentum_points += 5

if rsi_bullish_momentum or rsi_bearish_momentum
    momentum_points += 4

if (rsi_is_oversold and trend_bullish) or (rsi_is_overbought and not trend_bullish)
    momentum_points += 3

confidence_score += momentum_points

// 5. Market Regime Suitability (10 points)
regime_points = 0
if volatility_medium or volatility_high
    regime_points += 8
else if volatility_low
    regime_points += 5
else if volatility_extreme
    regime_points += 2

if bb_expansion
    regime_points += 2

confidence_score += regime_points

confidence_score := math.max(0, math.min(100, confidence_score))

// ============================================================================
// ENHANCED SIGNAL GENERATION LOGIC
// ============================================================================

// Signal mode adjustments
conf_threshold = signal_mode == "Conservative" ? min_confidence + 15 : signal_mode == "Aggressive" ? min_confidence - 10 : min_confidence

// Enhanced LONG Signal Conditions (Flexible Multi-Tier System)
long_structure_strong = bos_bullish_strong and trend_strength >= 2
long_structure_medium = (bos_bullish_weak or trend_bullish) and trend_strength >= 1
long_structure_weak = trend_bullish and trend_strength >= 0

long_technical_strong = ema_bullish_alignment and ema_trend_bullish and (rsi_bullish_momentum or macd_strong_bullish)
long_technical_medium = ema_bullish_alignment and (rsi_bullish_momentum or macd_bullish or close > ema_f)
long_technical_weak = close > ema_f and (rsi < 70 or macd_bullish)

long_smart_money_strong = volume_surge and (in_discount or in_equilibrium)
long_smart_money_medium = volume_high and not in_premium
long_smart_money_weak = volume_above_average

long_entry_strong = bb_lower_touch or (close > ema_m and close[1] <= ema_m)
long_entry_medium = bb_middle_cross_up or close > ema_f
long_entry_weak = close > close[1]



// LONG Signal Tiers (Multiple signal types)
long_signal_tier1 = long_structure_strong and long_technical_strong and long_smart_money_strong and long_entry_strong
long_signal_tier2 = (long_structure_strong and long_technical_medium and long_smart_money_medium) or (long_structure_medium and long_technical_strong and long_smart_money_strong)
long_signal_tier3 = (long_structure_medium and long_technical_medium and long_smart_money_medium and long_entry_medium) or (long_structure_weak and long_technical_strong and long_smart_money_strong and long_entry_strong)

// Alternative LONG signals when primary conditions not met
long_alternative = enable_alternative_signals and not (long_signal_tier1 or long_signal_tier2 or long_signal_tier3) and trend_bullish and close > ema_f and rsi_bullish_momentum and volume_above_average and (bb_middle_cross_up or close > ema_m)

// Enhanced SHORT Signal Conditions
short_structure_strong = bos_bearish_strong and trend_strength <= -2
short_structure_medium = (bos_bearish_weak or not trend_bullish) and trend_strength <= -1
short_structure_weak = not trend_bullish and trend_strength <= 0

short_technical_strong = ema_bearish_alignment and ema_trend_bearish and (rsi_bearish_momentum or macd_strong_bearish)
short_technical_medium = ema_bearish_alignment and (rsi_bearish_momentum or macd_bearish or close < ema_f)
short_technical_weak = close < ema_f and (rsi > 30 or macd_bearish)

short_smart_money_strong = volume_surge and (in_premium or in_equilibrium)
short_smart_money_medium = volume_high and not in_discount
short_smart_money_weak = volume_above_average

short_entry_strong = bb_upper_touch or (close < ema_m and close[1] >= ema_m)
short_entry_medium = bb_middle_cross_down or close < ema_f
short_entry_weak = close < close[1]

// SHORT Signal Tiers
short_signal_tier1 = short_structure_strong and short_technical_strong and short_smart_money_strong and short_entry_strong
short_signal_tier2 = (short_structure_strong and short_technical_medium and short_smart_money_medium) or (short_structure_medium and short_technical_strong and short_smart_money_strong)
short_signal_tier3 = (short_structure_medium and short_technical_medium and short_smart_money_medium and short_entry_medium) or (short_structure_weak and short_technical_strong and short_smart_money_strong and short_entry_strong)

// Alternative SHORT signals
short_alternative = enable_alternative_signals and not (short_signal_tier1 or short_signal_tier2 or short_signal_tier3) and not trend_bullish and close < ema_f and rsi_bearish_momentum and volume_above_average and (bb_middle_cross_down or close < ema_m)

// ============================================================================
// ENHANCED SCALPING ACCURACY FILTERS
// ============================================================================

// Market Session Filter (Optimized for Crypto/Forex)
current_hour = hour(time, "UTC+7")
// Prime scalping hours: Asian session overlap + European session
is_good_session = (current_hour >= 7 and current_hour <= 11) or (current_hour >= 14 and current_hour <= 18) or (current_hour >= 20 and current_hour <= 24)
session_filter_passed = not enable_market_session_filter or is_good_session

// Enhanced False Signal Filter for Scalping
price_range_10 = ta.highest(high, 10) - ta.lowest(low, 10) // Shorter range for scalping
avg_range_25 = ta.sma(price_range_10, 25)
price_volatility = price_range_10 / close * 100
is_trending_market = price_range_10 > avg_range_25 * 0.9 and price_volatility > 0.3
false_signal_filter_passed = not enable_false_signal_filter or is_trending_market

// Advanced Confluence Filter for Scalping
confluence_score = 0

// Signal Tier Scoring (Higher weight for better signals)
if (long_signal_tier1 or short_signal_tier1)
    confluence_score += 4
else if (long_signal_tier2 or short_signal_tier2)
    confluence_score += 3
else if (long_signal_tier3 or short_signal_tier3)
    confluence_score += 2

// Volume Confirmation (Critical for scalping)
if volume_breakout
    confluence_score += 3
else if volume_surge
    confluence_score += 2
else if volume_high
    confluence_score += 1

// Trend Alignment (Essential for scalping)
if (trend_bullish and close > ema_t and close > ema_s) or (not trend_bullish and close < ema_t and close < ema_s)
    confluence_score += 2
else if (trend_bullish and close > ema_t) or (not trend_bullish and close < ema_t)
    confluence_score += 1

// Momentum Confirmation
if rsi_momentum_strong and rsi_scalping_zone
    confluence_score += 2
else if rsi_bullish_momentum or rsi_bearish_momentum
    confluence_score += 1

// MACD Confirmation
if macd_strong_bullish or macd_strong_bearish
    confluence_score += 2
else if macd_bullish or macd_bearish
    confluence_score += 1

confluence_filter_passed = not enable_confluence_filter or confluence_score >= scalping_confluence_min

// Trend Momentum Filter (New for Scalping)
ema_slope_fast = (ema_f - ema_f[2]) / ema_f[2] * 100
ema_slope_medium = (ema_m - ema_m[3]) / ema_m[3] * 100
trend_momentum_strength = math.abs(ema_slope_fast) + math.abs(ema_slope_medium)
trend_momentum_filter_passed = not enable_trend_momentum_filter or trend_momentum_strength > momentum_threshold

// Price Action Filter (New for Scalping)
price_momentum = (close - close[2]) / close[2] * 100
price_acceleration = price_momentum > price_momentum[1]
candle_strength = math.abs(close - open) / (high - low)
strong_candle = candle_strength > 0.6
price_action_filter_passed = not enable_price_action_filter or (price_acceleration and strong_candle)

// Signal Gap Filter - Prevent too frequent signals
var int last_signal_bar = 0
current_bar = bar_index
signal_gap_ok = (current_bar - last_signal_bar) >= min_signal_gap

// Final signal determination with enhanced scalping filters
long_signal_raw = show_signals and confidence_score >= conf_threshold and (long_signal_tier1 or long_signal_tier2 or long_signal_tier3 or long_alternative)
short_signal_raw = show_signals and confidence_score >= conf_threshold and (short_signal_tier1 or short_signal_tier2 or short_signal_tier3 or short_alternative)

// Apply all scalping filters
all_filters_passed = session_filter_passed and false_signal_filter_passed and confluence_filter_passed and
                    trend_momentum_filter_passed and price_action_filter_passed and signal_gap_ok

long_signal_final = long_signal_raw and all_filters_passed
short_signal_final = short_signal_raw and all_filters_passed

// Update last signal bar
if long_signal_final or short_signal_final
    last_signal_bar := current_bar

// Signal quality classification with enhanced scoring
long_signal_quality = long_signal_tier1 ? "Premium" : long_signal_tier2 ? "High" : long_signal_tier3 ? "Good" : "Alternative"
short_signal_quality = short_signal_tier1 ? "Premium" : short_signal_tier2 ? "High" : short_signal_tier3 ? "Good" : "Alternative"





// ============================================================================
// ENHANCED RISK MANAGEMENT
// ============================================================================

// Entry Price
entry_price = close

// Enhanced Stop Loss Calculation (Scalping Optimized)
// Tighter stops for scalping with faster reaction
base_atr_multiplier = volatility_low ? 0.8 : volatility_medium ? 1.1 : volatility_high ? 1.4 : 1.8
volatility_adjustment = enable_volatility_adaptive ? (atr_pct / 4.0) : 0 // Reduced adjustment for scalping
atr_multiplier = base_atr_multiplier + volatility_adjustment
atr_stop_distance = atr * atr_multiplier

// Scalping-specific stop loss logic
scalping_stop_multiplier = enable_scalping_mode ? 0.7 : 1.0 // 30% tighter stops for scalping
atr_stop_distance := atr_stop_distance * scalping_stop_multiplier

// Structure-based stop loss with tighter levels for scalping
long_stop_loss = entry_price - atr_stop_distance
if not na(last_swing_low) and enable_scalping_mode
    structure_stop = last_swing_low * 0.9995 // Tighter structure stop for scalping
    long_stop_loss := math.max(long_stop_loss, structure_stop)
else if not na(last_swing_low)
    structure_stop = last_swing_low * 0.998
    long_stop_loss := math.max(long_stop_loss, structure_stop)

short_stop_loss = entry_price + atr_stop_distance
if not na(last_swing_high) and enable_scalping_mode
    structure_stop = last_swing_high * 1.0005 // Tighter structure stop for scalping
    short_stop_loss := math.min(short_stop_loss, structure_stop)
else if not na(last_swing_high)
    structure_stop = last_swing_high * 1.002
    short_stop_loss := math.min(short_stop_loss, structure_stop)

// Enhanced Take Profit with scalping-optimized R:R ratios
base_rr_multiplier = 0.0
if enable_scalping_mode
    // Scalping R:R ratios (faster, smaller targets)
    base_rr_multiplier := long_signal_quality == "Premium" or short_signal_quality == "Premium" ? 2.0 :
                         long_signal_quality == "High" or short_signal_quality == "High" ? 1.8 :
                         long_signal_quality == "Good" or short_signal_quality == "Good" ? 1.6 : 1.4
else
    // Standard R:R ratios
    base_rr_multiplier := long_signal_quality == "Premium" or short_signal_quality == "Premium" ? 2.5 :
                         long_signal_quality == "High" or short_signal_quality == "High" ? 2.2 :
                         long_signal_quality == "Good" or short_signal_quality == "Good" ? 2.0 : 1.8

// Scalping-optimized take profit levels
if enable_scalping_mode
    // Closer, more achievable targets for scalping
    long_tp1 = entry_price + (entry_price - long_stop_loss) * base_rr_multiplier
    long_tp2 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 0.6)
    long_tp3 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 1.2)

    short_tp1 = entry_price - (short_stop_loss - entry_price) * base_rr_multiplier
    short_tp2 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 0.6)
    short_tp3 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 1.2)
else
    // Standard take profit levels
    long_tp1 = entry_price + (entry_price - long_stop_loss) * base_rr_multiplier
    long_tp2 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 1.0)
    long_tp3 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 2.5)

    short_tp1 = entry_price - (short_stop_loss - entry_price) * base_rr_multiplier
    short_tp2 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 1.0)
    short_tp3 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 2.5)

// Risk:Reward validation
long_rr_ratio = (long_tp1 - entry_price) / (entry_price - long_stop_loss)
short_rr_ratio = (entry_price - short_tp1) / (short_stop_loss - entry_price)

long_valid_rr = long_rr_ratio >= risk_reward_min
short_valid_rr = short_rr_ratio >= risk_reward_min

// Final signal validation
final_long_signal = long_signal_final and long_valid_rr
final_short_signal = short_signal_final and short_valid_rr

// ============================================================================
// CLEAN VISUAL ELEMENTS
// ============================================================================

// Plot EMAs with enhanced styling
plot(ema_f, "EMA Fast", color=show_ema_lines ? color.new(color.blue, 40) : na, linewidth=1)
plot(ema_m, "EMA Medium", color=show_ema_lines ? color.new(color.orange, 30) : na, linewidth=2)
plot(ema_s, "EMA Slow", color=show_ema_lines ? color.new(color.red, 40) : na, linewidth=2)
plot(ema_t, "EMA Trend", color=show_ema_lines ? color.new(color.purple, 50) : na, linewidth=2)

// Plot Bollinger Bands
p1 = plot(bb_upper, "BB Upper", color=show_bb_lines ? color.new(color.gray, 70) : na, linewidth=1)
p2 = plot(bb_lower, "BB Lower", color=show_bb_lines ? color.new(color.gray, 70) : na, linewidth=1)
fill(p1, p2, color=show_bb_lines ? color.new(color.gray, 95) : na, title="BB Fill")

// ============================================================================
// ENHANCED SIGNAL DISPLAY SYSTEM
// ============================================================================

// Signal tracking for display management
var array<int> signal_bars = array.new<int>()
var array<string> signal_types = array.new<string>()
var array<float> signal_prices = array.new<float>()

label_size = signal_label_size == "Small" ? size.small : signal_label_size == "Large" ? size.large : size.normal

// Function to clean old signals
cleanup_old_signals() =>
    if array.size(signal_bars) > max_signals_display
        array.shift(signal_bars)
        array.shift(signal_types)
        array.shift(signal_prices)

// Enhanced LONG Signal Display
if final_long_signal
    // Add to tracking arrays
    array.push(signal_bars, bar_index)
    array.push(signal_types, "LONG_" + long_signal_quality)
    array.push(signal_prices, entry_price)
    cleanup_old_signals()

    // Create signal text based on compact setting
    signal_text = ""
    if compact_labels
        signal_text := "🟢 " + long_signal_quality + " LONG\n" +
                      "Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                      "SL: " + str.tostring(long_stop_loss, "#.####") + "\n" +
                      "TP1: " + str.tostring(long_tp1, "#.####") + "\n" +
                      "TP2: " + str.tostring(long_tp2, "#.####") + "\n" +
                      "TP3: " + str.tostring(long_tp3, "#.####") + "\n" +
                      "Conf: " + str.tostring(confidence_score) + "%"
    else
        signal_text := "🟢 " + long_signal_quality + " LONG\n" +
                      "━━━━━━━━━━━━━━━━━━━━\n" +
                      "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                      "🛑 Stop Loss: " + str.tostring(long_stop_loss, "#.####") + "\n" +
                      "━━━━━━━━━━━━━━━━━━━━\n" +
                      "🎯 TP1: " + str.tostring(long_tp1, "#.####") + "\n" +
                      "🎯 TP2: " + str.tostring(long_tp2, "#.####") + "\n" +
                      "🎯 TP3: " + str.tostring(long_tp3, "#.####") + "\n" +
                      "━━━━━━━━━━━━━━━━━━━━\n" +
                      "📊 Confidence: " + str.tostring(confidence_score) + "%\n" +
                      "🔥 Quality: " + long_signal_quality

    label_color = long_signal_quality == "Premium" ? color.new(color.lime, 10) :
                  long_signal_quality == "High" ? color.new(color.green, 10) :
                  long_signal_quality == "Good" ? color.new(color.green, 20) : color.new(color.green, 30)

    // Only show if show_all_signals is true or this is the latest signal
    should_show = show_all_signals or array.size(signal_bars) == 1
    if should_show
        label.new(bar_index, low - atr*1.5, text=signal_text, style=label.style_label_up,
                 color=label_color, textcolor=color.white, size=label_size)

// Enhanced SHORT Signal Display
if final_short_signal
    // Add to tracking arrays
    array.push(signal_bars, bar_index)
    array.push(signal_types, "SHORT_" + short_signal_quality)
    array.push(signal_prices, entry_price)
    cleanup_old_signals()

    // Create signal text based on compact setting
    signal_text = ""
    if compact_labels
        signal_text := "🔴 " + short_signal_quality + " SHORT\n" +
                      "Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                      "SL: " + str.tostring(short_stop_loss, "#.####") + "\n" +
                      "TP1: " + str.tostring(short_tp1, "#.####") + "\n" +
                      "TP2: " + str.tostring(short_tp2, "#.####") + "\n" +
                      "TP3: " + str.tostring(short_tp3, "#.####") + "\n" +
                      "Conf: " + str.tostring(confidence_score) + "%"
    else
        signal_text := "🔴 " + short_signal_quality + " SHORT\n" +
                      "━━━━━━━━━━━━━━━━━━━━\n" +
                      "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                      "🛑 Stop Loss: " + str.tostring(short_stop_loss, "#.####") + "\n" +
                      "━━━━━━━━━━━━━━━━━━━━\n" +
                      "🎯 TP1: " + str.tostring(short_tp1, "#.####") + "\n" +
                      "🎯 TP2: " + str.tostring(short_tp2, "#.####") + "\n" +
                      "🎯 TP3: " + str.tostring(short_tp3, "#.####") + "\n" +
                      "━━━━━━━━━━━━━━━━━━━━\n" +
                      "📊 Confidence: " + str.tostring(confidence_score) + "%\n" +
                      "🔥 Quality: " + short_signal_quality

    label_color = short_signal_quality == "Premium" ? color.new(color.red, 10) :
                  short_signal_quality == "High" ? color.new(color.red, 10) :
                  short_signal_quality == "Good" ? color.new(color.red, 20) : color.new(color.red, 30)

    // Only show if show_all_signals is true or this is the latest signal
    should_show = show_all_signals or array.size(signal_bars) == 1
    if should_show
        label.new(bar_index, high + atr*1.5, text=signal_text, style=label.style_label_down,
                 color=label_color, textcolor=color.white, size=label_size)

// Enhanced Confidence Badge (only for latest signal if not showing all)
show_badge = show_confidence and (final_long_signal or final_short_signal)
if show_badge and (show_all_signals or not show_all_signals)
    conf_color = confidence_score >= 85 ? color.new(color.lime, 20) :
                 confidence_score >= 75 ? color.new(color.green, 20) :
                 confidence_score >= 65 ? color.new(color.yellow, 20) : color.new(color.orange, 20)

    conf_text = "📊 " + str.tostring(confidence_score) + "%"
    badge_y = final_long_signal ? low - atr*3 : high + atr*3

    if not compact_labels
        label.new(bar_index, badge_y, text=conf_text, style=label.style_label_center,
                 color=conf_color, textcolor=color.white, size=size.small)

// ============================================================================
// ENHANCED VISUAL ELEMENTS
// ============================================================================

// Draw TP/SL lines for latest signal (similar to image)
if final_long_signal and show_all_signals
    // Entry line
    line.new(bar_index, entry_price, bar_index + 10, entry_price,
             color=color.new(color.white, 30), width=1, style=line.style_dashed)

    // TP lines
    line.new(bar_index, long_tp1, bar_index + 10, long_tp1,
             color=color.new(color.blue, 30), width=1, style=line.style_solid)
    line.new(bar_index, long_tp2, bar_index + 10, long_tp2,
             color=color.new(color.blue, 30), width=1, style=line.style_solid)
    line.new(bar_index, long_tp3, bar_index + 10, long_tp3,
             color=color.new(color.blue, 30), width=1, style=line.style_solid)

    // SL line
    line.new(bar_index, long_stop_loss, bar_index + 10, long_stop_loss,
             color=color.new(color.red, 30), width=1, style=line.style_solid)

if final_short_signal and show_all_signals
    // Entry line
    line.new(bar_index, entry_price, bar_index + 10, entry_price,
             color=color.new(color.white, 30), width=1, style=line.style_dashed)

    // TP lines
    line.new(bar_index, short_tp1, bar_index + 10, short_tp1,
             color=color.new(color.blue, 30), width=1, style=line.style_solid)
    line.new(bar_index, short_tp2, bar_index + 10, short_tp2,
             color=color.new(color.blue, 30), width=1, style=line.style_solid)
    line.new(bar_index, short_tp3, bar_index + 10, short_tp3,
             color=color.new(color.blue, 30), width=1, style=line.style_solid)

    // SL line
    line.new(bar_index, short_stop_loss, bar_index + 10, short_stop_loss,
             color=color.new(color.red, 30), width=1, style=line.style_solid)

// ============================================================================
// ALERT CONDITIONS
// ============================================================================

alertcondition(final_long_signal, "Enhanced Long Signal", "🟢 Enhanced LONG Signal Generated")
alertcondition(final_short_signal, "Enhanced Short Signal", "🔴 Enhanced SHORT Signal Generated")
alertcondition(final_long_signal and long_signal_quality == "Premium", "Premium Long Signal", "🟢 PREMIUM LONG Signal Generated")
alertcondition(final_short_signal and short_signal_quality == "Premium", "Premium Short Signal", "🔴 PREMIUM SHORT Signal Generated")

// ============================================================================
// ENHANCED STRATEGY NOTES
// ============================================================================

// Enhanced Strategy Features:
// 1. ✅ Multi-tier signal system (Premium/High/Good/Alternative)
// 2. ✅ Enhanced confidence scoring with better distribution
// 3. ✅ Flexible signal conditions - not requiring all criteria
// 4. ✅ Alternative signal types when primary conditions not met
// 5. ✅ Dynamic risk:reward ratios based on signal quality
// 6. ✅ Volatility-adaptive stop losses
// 7. ✅ Enhanced momentum and trend strength analysis
// 8. ✅ Optimized parameters for crypto trading
// 9. ✅ Signal mode selection (Conservative/Balanced/Aggressive)
// 10. ✅ Advanced volume and market structure analysis
// 11. ✅ Enhanced accuracy filters (Confluence, False Signal, Market Session)
// 12. ✅ Flexible signal display options (All signals vs Latest only)
// 13. ✅ Compact and detailed label formats
// 14. ✅ Signal gap control to prevent over-trading
// 15. ✅ Visual TP/SL lines for better trade management

// Key Improvements V2:
// - Significantly improved accuracy with multi-layer filtering
// - Better signal timing with confluence requirements
// - Reduced false signals through market condition analysis
// - Enhanced visual presentation with flexible display options
// - Professional-grade signal management system
// - Optimized for both scalping and swing trading

// Credits: Trading Signal Bot - Enhanced Strategy Version
// Optimized for high accuracy and optimal signal frequency
