//@version=5
indicator("Trading Signal Bot - Enhanced Strategy", shorttitle="TSB-Enhanced", overlay=true, max_boxes_count=10, max_lines_count=10, max_labels_count=50)

// ============================================================================
// TRADING SIGNAL BOT - ENHANCED STRATEGY VERSION
// Cải tiến độ chính xác và tần suất tín hiệu dựa trên phân tích chiến lược
// Tối ưu hóa cho crypto trading với win rate cao và signal frequency tốt
// ============================================================================

// ============================================================================
// CẤU HÌNH THAM SỐ ĐÃ ĐƯỢC TỐI ƯU HÓA
// ============================================================================

// Technical Indicators Settings (Optimized)
group_ti = "📊 Technical Indicators"
ema_fast = input.int(8, "EMA Fast", minval=5, maxval=15, group=group_ti)
ema_medium = input.int(21, "EMA Medium", minval=15, maxval=30, group=group_ti)
ema_slow = input.int(50, "EMA Slow", minval=40, maxval=60, group=group_ti)
ema_trend = input.int(200, "EMA Trend", minval=150, maxval=250, group=group_ti)
rsi_length = input.int(14, "RSI Length", minval=10, maxval=20, group=group_ti)
rsi_oversold = input.int(35, "RSI Oversold Level", minval=25, maxval=40, group=group_ti)
rsi_overbought = input.int(65, "RSI Overbought Level", minval=60, maxval=75, group=group_ti)
macd_fast = input.int(12, "MACD Fast", minval=8, maxval=15, group=group_ti)
macd_slow = input.int(26, "MACD Slow", minval=20, maxval=30, group=group_ti)
macd_signal = input.int(9, "MACD Signal", minval=7, maxval=12, group=group_ti)
bb_length = input.int(20, "Bollinger Bands Length", minval=15, maxval=25, group=group_ti)
bb_mult = input.float(2.0, "Bollinger Bands Multiplier", minval=1.5, maxval=2.5, group=group_ti)
atr_length = input.int(14, "ATR Length", minval=10, maxval=20, group=group_ti)

// Enhanced Signal Settings
group_sig = "🎯 Enhanced Signal Generation"
min_confidence = input.int(65, "Minimum Confidence Score", minval=50, maxval=90, group=group_sig)
signal_mode = input.string("Balanced", "Signal Mode", options=["Conservative", "Balanced", "Aggressive"], group=group_sig)
show_signals = input.bool(true, "Show Trading Signals", group=group_sig)
show_confidence = input.bool(true, "Show Confidence Score", group=group_sig)
risk_reward_min = input.float(1.8, "Minimum Risk:Reward Ratio", minval=1.5, maxval=3.0, group=group_sig)
structure_lookback = input.int(15, "Structure Lookback Period", minval=10, maxval=25, group=group_sig)
range_lookback = input.int(40, "Range Lookback", minval=30, maxval=60, group=group_sig)

// Advanced Features
group_adv = "🔬 Advanced Features"
enable_momentum_filter = input.bool(true, "Enable Momentum Filter", group=group_adv)
enable_volatility_adaptive = input.bool(true, "Enable Volatility Adaptive", group=group_adv)
enable_trend_strength = input.bool(true, "Enable Trend Strength Analysis", group=group_adv)
enable_alternative_signals = input.bool(true, "Enable Alternative Signal Types", group=group_adv)

// Visual Settings
group_vis = "🎨 Visual Settings"
signal_label_size = input.string("Normal", "Signal Label Size", options=["Small", "Normal", "Large"], group=group_vis)
show_ema_lines = input.bool(true, "Show EMA Lines", group=group_vis)
show_bb_lines = input.bool(false, "Show Bollinger Bands", group=group_vis)

// ============================================================================
// ENHANCED TECHNICAL INDICATORS
// ============================================================================

// Optimized Moving Averages
ema_f = ta.ema(close, ema_fast)
ema_m = ta.ema(close, ema_medium)
ema_s = ta.ema(close, ema_slow)
ema_t = ta.ema(close, ema_trend)

// Enhanced RSI with dynamic levels
rsi = ta.rsi(close, rsi_length)
rsi_momentum = ta.rsi(close, 7) // Fast RSI for momentum
rsi_is_oversold = rsi < rsi_oversold
rsi_is_overbought = rsi > rsi_overbought
rsi_bullish_momentum = rsi_momentum > 50 and rsi_momentum[1] <= 50
rsi_bearish_momentum = rsi_momentum < 50 and rsi_momentum[1] >= 50

// Enhanced MACD with histogram analysis
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line
macd_bearish = macd_line < signal_line
macd_histogram_increasing = histogram > histogram[1]
macd_histogram_decreasing = histogram < histogram[1]
macd_strong_bullish = macd_bullish and macd_histogram_increasing and histogram > 0
macd_strong_bearish = macd_bearish and macd_histogram_decreasing and histogram < 0

// Enhanced Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
bb_expansion = (bb_upper - bb_lower) / bb_middle > 0.15
bb_upper_touch = high >= bb_upper * 0.999
bb_lower_touch = low <= bb_lower * 1.001
bb_middle_cross_up = close > bb_middle and close[1] <= bb_middle
bb_middle_cross_down = close < bb_middle and close[1] >= bb_middle

// Enhanced ATR and Volatility
atr = ta.atr(atr_length)
atr_pct = (atr / close) * 100
volatility_low = atr_pct < 1.5
volatility_medium = atr_pct >= 1.5 and atr_pct < 3.0
volatility_high = atr_pct >= 3.0 and atr_pct < 5.0
volatility_extreme = atr_pct >= 5.0

// Enhanced Volume Analysis
volume_sma = ta.sma(volume, 20)
volume_ema = ta.ema(volume, 10)
volume_surge = volume > volume_sma * 1.8
volume_high = volume > volume_sma * 1.3
volume_above_average = volume > volume_ema
volume_trend_up = volume_ema > volume_ema[3]

// ============================================================================
// ENHANCED MARKET STRUCTURE ANALYSIS
// ============================================================================

// Optimized Swing Detection
swing_high = ta.pivothigh(high, structure_lookback, structure_lookback)
swing_low = ta.pivotlow(low, structure_lookback, structure_lookback)

// Enhanced BOS Detection
var float last_swing_high = na
var float last_swing_low = na
var float prev_swing_high = na
var float prev_swing_low = na

if not na(swing_high)
    prev_swing_high := last_swing_high
    last_swing_high := swing_high

if not na(swing_low)
    prev_swing_low := last_swing_low
    last_swing_low := swing_low

// Multiple BOS confirmation levels
bos_bullish_strong = not na(last_swing_high) and close > last_swing_high and close[1] <= last_swing_high
bos_bearish_strong = not na(last_swing_low) and close < last_swing_low and close[1] >= last_swing_low
bos_bullish_weak = not na(prev_swing_high) and close > prev_swing_high and not bos_bullish_strong
bos_bearish_weak = not na(prev_swing_low) and close < prev_swing_low and not bos_bearish_strong

// Enhanced Trend Analysis
var bool trend_bullish = true
var int trend_strength = 0

// Trend strength calculation
if bos_bullish_strong
    trend_bullish := true
    trend_strength := math.min(trend_strength + 2, 5)
else if bos_bearish_strong
    trend_bullish := false
    trend_strength := math.max(trend_strength - 2, -5)
else if bos_bullish_weak
    trend_strength := math.min(trend_strength + 1, 5)
else if bos_bearish_weak
    trend_strength := math.max(trend_strength - 1, -5)

// Premium/Discount Zones with multiple timeframes
range_high = ta.highest(high, range_lookback)
range_low = ta.lowest(low, range_lookback)
range_mid = (range_high + range_low) / 2
range_quarter_upper = range_mid + (range_high - range_mid) * 0.5
range_quarter_lower = range_mid - (range_mid - range_low) * 0.5

in_premium = close > range_quarter_upper
in_discount = close < range_quarter_lower
in_equilibrium = close >= range_quarter_lower and close <= range_quarter_upper

// ============================================================================
// ENHANCED CONFIDENCE SCORING SYSTEM
// ============================================================================

// Base confidence score increased
confidence_score = 40 // Increased base score

// 1. Enhanced Market Structure Analysis (30 points)
structure_points = 0
if bos_bullish_strong or bos_bearish_strong
    structure_points += 20
else if bos_bullish_weak or bos_bearish_weak
    structure_points += 10

if trend_strength >= 3 or trend_strength <= -3
    structure_points += 10
else if trend_strength >= 2 or trend_strength <= -2
    structure_points += 5

confidence_score += structure_points

// 2. Enhanced Smart Money Confirmation (25 points)
smart_money_points = 0
if volume_surge
    smart_money_points += 15
else if volume_high
    smart_money_points += 10
else if volume_above_average
    smart_money_points += 5

if volume_trend_up
    smart_money_points += 5

if (trend_bullish and in_discount) or (not trend_bullish and in_premium)
    smart_money_points += 5

confidence_score += smart_money_points

// 3. Enhanced Technical Alignment (20 points)
technical_points = 0
ema_bullish_alignment = ema_f > ema_m and ema_m > ema_s
ema_bearish_alignment = ema_f < ema_m and ema_m < ema_s
ema_trend_bullish = ema_s > ema_t
ema_trend_bearish = ema_s < ema_t

if (ema_bullish_alignment and ema_trend_bullish) or (ema_bearish_alignment and ema_trend_bearish)
    technical_points += 15
else if ema_bullish_alignment or ema_bearish_alignment
    technical_points += 10

if enable_trend_strength
    price_above_ema_f = close > ema_f
    price_below_ema_f = close < ema_f
    if (trend_bullish and price_above_ema_f) or (not trend_bullish and price_below_ema_f)
        technical_points += 5

confidence_score += technical_points

// 4. Enhanced Momentum Indicators (15 points)
momentum_points = 0
if macd_strong_bullish or macd_strong_bearish
    momentum_points += 8
else if macd_bullish or macd_bearish
    momentum_points += 5

if rsi_bullish_momentum or rsi_bearish_momentum
    momentum_points += 4

if (rsi_is_oversold and trend_bullish) or (rsi_is_overbought and not trend_bullish)
    momentum_points += 3

confidence_score += momentum_points

// 5. Market Regime Suitability (10 points)
regime_points = 0
if volatility_medium or volatility_high
    regime_points += 8
else if volatility_low
    regime_points += 5
else if volatility_extreme
    regime_points += 2

if bb_expansion
    regime_points += 2

confidence_score += regime_points

confidence_score := math.max(0, math.min(100, confidence_score))

// ============================================================================
// ENHANCED SIGNAL GENERATION LOGIC
// ============================================================================

// Signal mode adjustments
conf_threshold = signal_mode == "Conservative" ? min_confidence + 15 : signal_mode == "Aggressive" ? min_confidence - 10 : min_confidence

// Enhanced LONG Signal Conditions (Flexible Multi-Tier System)
long_structure_strong = bos_bullish_strong and trend_strength >= 2
long_structure_medium = (bos_bullish_weak or trend_bullish) and trend_strength >= 1
long_structure_weak = trend_bullish and trend_strength >= 0

long_technical_strong = ema_bullish_alignment and ema_trend_bullish and (rsi_bullish_momentum or macd_strong_bullish)
long_technical_medium = ema_bullish_alignment and (rsi_bullish_momentum or macd_bullish or close > ema_f)
long_technical_weak = close > ema_f and (rsi < 70 or macd_bullish)

long_smart_money_strong = volume_surge and (in_discount or in_equilibrium)
long_smart_money_medium = volume_high and not in_premium
long_smart_money_weak = volume_above_average

long_entry_strong = bb_lower_touch or (close > ema_m and close[1] <= ema_m)
long_entry_medium = bb_middle_cross_up or close > ema_f
long_entry_weak = close > close[1]

// ============================================================================
// ENHANCED SIGNAL GENERATION LOGIC
// ============================================================================

// Signal mode adjustments
conf_threshold = signal_mode == "Conservative" ? min_confidence + 15 :
                 signal_mode == "Aggressive" ? min_confidence - 10 : min_confidence

// Enhanced LONG Signal Conditions (Flexible Multi-Tier System)
long_structure_strong = bos_bullish_strong and trend_strength >= 2
long_structure_medium = (bos_bullish_weak or trend_bullish) and trend_strength >= 1
long_structure_weak = trend_bullish and trend_strength >= 0

long_technical_strong = ema_bullish_alignment and ema_trend_bullish and (rsi_bullish_momentum or macd_strong_bullish)
long_technical_medium = ema_bullish_alignment and (rsi_bullish_momentum or macd_bullish or close > ema_f)
long_technical_weak = close > ema_f and (rsi < 70 or macd_bullish)

long_smart_money_strong = volume_surge and (in_discount or in_equilibrium)
long_smart_money_medium = volume_high and not in_premium
long_smart_money_weak = volume_above_average

long_entry_strong = bb_lower_touch or (close > ema_m and close[1] <= ema_m)
long_entry_medium = bb_middle_cross_up or close > ema_f
long_entry_weak = close > close[1]

// LONG Signal Tiers (Multiple signal types)
long_signal_tier1 = long_structure_strong and long_technical_strong and long_smart_money_strong and long_entry_strong
long_signal_tier2 = (long_structure_strong and long_technical_medium and long_smart_money_medium) or (long_structure_medium and long_technical_strong and long_smart_money_strong)
long_signal_tier3 = (long_structure_medium and long_technical_medium and long_smart_money_medium and long_entry_medium) or (long_structure_weak and long_technical_strong and long_smart_money_strong and long_entry_strong)

// Alternative LONG signals when primary conditions not met
long_alternative = enable_alternative_signals and not (long_signal_tier1 or long_signal_tier2 or long_signal_tier3) and trend_bullish and close > ema_f and rsi_bullish_momentum and volume_above_average and (bb_middle_cross_up or close > ema_m)

// Enhanced SHORT Signal Conditions
short_structure_strong = bos_bearish_strong and trend_strength <= -2
short_structure_medium = (bos_bearish_weak or not trend_bullish) and trend_strength <= -1
short_structure_weak = not trend_bullish and trend_strength <= 0

short_technical_strong = ema_bearish_alignment and ema_trend_bearish and (rsi_bearish_momentum or macd_strong_bearish)
short_technical_medium = ema_bearish_alignment and (rsi_bearish_momentum or macd_bearish or close < ema_f)
short_technical_weak = close < ema_f and (rsi > 30 or macd_bearish)

short_smart_money_strong = volume_surge and (in_premium or in_equilibrium)
short_smart_money_medium = volume_high and not in_discount
short_smart_money_weak = volume_above_average

short_entry_strong = bb_upper_touch or (close < ema_m and close[1] >= ema_m)
short_entry_medium = bb_middle_cross_down or close < ema_f
short_entry_weak = close < close[1]

// SHORT Signal Tiers
short_signal_tier1 = short_structure_strong and short_technical_strong and short_smart_money_strong and short_entry_strong
short_signal_tier2 = (short_structure_strong and short_technical_medium and short_smart_money_medium) or (short_structure_medium and short_technical_strong and short_smart_money_strong)
short_signal_tier3 = (short_structure_medium and short_technical_medium and short_smart_money_medium and short_entry_medium) or (short_structure_weak and short_technical_strong and short_smart_money_strong and short_entry_strong)

// Alternative SHORT signals
short_alternative = enable_alternative_signals and not (short_signal_tier1 or short_signal_tier2 or short_signal_tier3) and not trend_bullish and close < ema_f and rsi_bearish_momentum and volume_above_average and (bb_middle_cross_down or close < ema_m)

// Final signal determination with confidence adjustment
long_signal_final = show_signals and confidence_score >= conf_threshold and (long_signal_tier1 or long_signal_tier2 or long_signal_tier3 or long_alternative)
short_signal_final = show_signals and confidence_score >= conf_threshold and (short_signal_tier1 or short_signal_tier2 or short_signal_tier3 or short_alternative)

// Signal quality classification
long_signal_quality = long_signal_tier1 ? "Premium" : long_signal_tier2 ? "High" : long_signal_tier3 ? "Good" : "Alternative"
short_signal_quality = short_signal_tier1 ? "Premium" : short_signal_tier2 ? "High" : short_signal_tier3 ? "Good" : "Alternative"

// ============================================================================
// ENHANCED RISK MANAGEMENT
// ============================================================================

// Entry Price
entry_price = close

// Enhanced Stop Loss Calculation
base_atr_multiplier = volatility_low ? 1.2 : volatility_medium ? 1.6 : volatility_high ? 2.2 : 2.8
volatility_adjustment = enable_volatility_adaptive ? (atr_pct / 3.0) : 0
atr_multiplier = base_atr_multiplier + volatility_adjustment
atr_stop_distance = atr * atr_multiplier

// Structure-based stop loss with multiple levels
long_stop_loss = entry_price - atr_stop_distance
if not na(last_swing_low)
    structure_stop = last_swing_low * 0.998
    long_stop_loss := math.max(long_stop_loss, structure_stop)

short_stop_loss = entry_price + atr_stop_distance
if not na(last_swing_high)
    structure_stop = last_swing_high * 1.002
    short_stop_loss := math.min(short_stop_loss, structure_stop)

// Enhanced Take Profit with dynamic R:R based on signal quality
base_rr_multiplier = long_signal_quality == "Premium" or short_signal_quality == "Premium" ? 2.5 : long_signal_quality == "High" or short_signal_quality == "High" ? 2.2 : long_signal_quality == "Good" or short_signal_quality == "Good" ? 2.0 : 1.8

long_tp1 = entry_price + (entry_price - long_stop_loss) * base_rr_multiplier
long_tp2 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 1.0)
long_tp3 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 2.5)

short_tp1 = entry_price - (short_stop_loss - entry_price) * base_rr_multiplier
short_tp2 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 1.0)
short_tp3 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 2.5)

// Risk:Reward validation
long_rr_ratio = (long_tp1 - entry_price) / (entry_price - long_stop_loss)
short_rr_ratio = (entry_price - short_tp1) / (short_stop_loss - entry_price)

long_valid_rr = long_rr_ratio >= risk_reward_min
short_valid_rr = short_rr_ratio >= risk_reward_min

// Final signal validation
final_long_signal = long_signal_final and long_valid_rr
final_short_signal = short_signal_final and short_valid_rr

// ============================================================================
// CLEAN VISUAL ELEMENTS
// ============================================================================

// Plot EMAs with enhanced styling
plot(ema_f, "EMA Fast", color=show_ema_lines ? color.new(color.blue, 40) : na, linewidth=1)
plot(ema_m, "EMA Medium", color=show_ema_lines ? color.new(color.orange, 30) : na, linewidth=2)
plot(ema_s, "EMA Slow", color=show_ema_lines ? color.new(color.red, 40) : na, linewidth=2)
plot(ema_t, "EMA Trend", color=show_ema_lines ? color.new(color.purple, 50) : na, linewidth=2)

// Plot Bollinger Bands
p1 = plot(bb_upper, "BB Upper", color=show_bb_lines ? color.new(color.gray, 70) : na, linewidth=1)
p2 = plot(bb_lower, "BB Lower", color=show_bb_lines ? color.new(color.gray, 70) : na, linewidth=1)
fill(p1, p2, color=show_bb_lines ? color.new(color.gray, 95) : na, title="BB Fill")

// ============================================================================
// ENHANCED SIGNAL LABELS
// ============================================================================

label_size = signal_label_size == "Small" ? size.small : signal_label_size == "Large" ? size.large : size.normal

// Enhanced LONG Signal Label
if final_long_signal
    signal_text = "🟢 " + long_signal_quality + " LONG\n" + "━━━━━━━━━━━━━━━━━━━━\n" + "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" + "🛑 Stop Loss: " + str.tostring(long_stop_loss, "#.####") + "\n" + "━━━━━━━━━━━━━━━━━━━━\n" + "🎯 TP1: " + str.tostring(long_tp1, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier, "#.#") + ")\n" + "🎯 TP2: " + str.tostring(long_tp2, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 1.0, "#.#") + ")\n" + "🎯 TP3: " + str.tostring(long_tp3, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 2.5, "#.#") + ")\n" + "━━━━━━━━━━━━━━━━━━━━\n" + "📊 Confidence: " + str.tostring(confidence_score) + "%\n" + "🔥 Quality: " + long_signal_quality + "\n" + "📈 Trend: " + str.tostring(trend_strength)

    label_color = long_signal_quality == "Premium" ? color.new(color.lime, 5) : long_signal_quality == "High" ? color.new(color.green, 5) : long_signal_quality == "Good" ? color.new(color.green, 15) : color.new(color.green, 25)

    label.new(bar_index, low - atr*2, text=signal_text, style=label.style_label_up, color=label_color, textcolor=color.white, size=label_size)

// Enhanced SHORT Signal Label
if final_short_signal
    signal_text = "🔴 " + short_signal_quality + " SHORT\n" + "━━━━━━━━━━━━━━━━━━━━\n" + "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" + "🛑 Stop Loss: " + str.tostring(short_stop_loss, "#.####") + "\n" + "━━━━━━━━━━━━━━━━━━━━\n" + "🎯 TP1: " + str.tostring(short_tp1, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier, "#.#") + ")\n" + "🎯 TP2: " + str.tostring(short_tp2, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 1.0, "#.#") + ")\n" + "🎯 TP3: " + str.tostring(short_tp3, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 2.5, "#.#") + ")\n" + "━━━━━━━━━━━━━━━━━━━━\n" + "📊 Confidence: " + str.tostring(confidence_score) + "%\n" + "🔥 Quality: " + short_signal_quality + "\n" + "📉 Trend: " + str.tostring(trend_strength)

    label_color = short_signal_quality == "Premium" ? color.new(color.red, 5) : short_signal_quality == "High" ? color.new(color.red, 5) : short_signal_quality == "Good" ? color.new(color.red, 15) : color.new(color.red, 25)

    label.new(bar_index, high + atr*2, text=signal_text, style=label.style_label_down, color=label_color, textcolor=color.white, size=label_size)

// Enhanced Confidence Badge
if show_confidence and (final_long_signal or final_short_signal)
    conf_color = confidence_score >= 85 ? color.new(color.lime, 10) : confidence_score >= 75 ? color.new(color.green, 10) : confidence_score >= 65 ? color.new(color.yellow, 10) : color.new(color.orange, 10)

    conf_text = "📊 " + str.tostring(confidence_score) + "%\n🎯 " + (final_long_signal ? long_signal_quality : short_signal_quality)
    badge_y = final_long_signal ? low - atr*4.5 : high + atr*4.5

    label.new(bar_index, badge_y, text=conf_text, style=label.style_label_center, color=conf_color, textcolor=color.white, size=size.small)

// ============================================================================
// ALERT CONDITIONS
// ============================================================================

alertcondition(final_long_signal, "Enhanced Long Signal", "🟢 Enhanced LONG Signal Generated")
alertcondition(final_short_signal, "Enhanced Short Signal", "🔴 Enhanced SHORT Signal Generated")
alertcondition(final_long_signal and long_signal_quality == "Premium", "Premium Long Signal", "🟢 PREMIUM LONG Signal Generated")
alertcondition(final_short_signal and short_signal_quality == "Premium", "Premium Short Signal", "🔴 PREMIUM SHORT Signal Generated")

// ============================================================================
// ENHANCED STRATEGY NOTES
// ============================================================================

// Enhanced Strategy Features:
// 1. ✅ Multi-tier signal system (Premium/High/Good/Alternative)
// 2. ✅ Enhanced confidence scoring with better distribution
// 3. ✅ Flexible signal conditions - not requiring all criteria
// 4. ✅ Alternative signal types when primary conditions not met
// 5. ✅ Dynamic risk:reward ratios based on signal quality
// 6. ✅ Volatility-adaptive stop losses
// 7. ✅ Enhanced momentum and trend strength analysis
// 8. ✅ Optimized parameters for crypto trading
// 9. ✅ Signal mode selection (Conservative/Balanced/Aggressive)
// 10. ✅ Advanced volume and market structure analysis

// Key Improvements:
// - Increased signal frequency while maintaining quality
// - Better entry timing with multiple confirmation levels
// - Enhanced risk management with dynamic adjustments
// - Reduced false signals through multi-tier validation
// - Alternative signals for additional opportunities

// Credits: Trading Signal Bot - Enhanced Strategy Version
// Optimized for high accuracy and optimal signal frequency



// ============================================================================
// ENHANCED RISK MANAGEMENT
// ============================================================================

// Entry Price
entry_price = close

// Enhanced Stop Loss Calculation
base_atr_multiplier = volatility_low ? 1.2 : volatility_medium ? 1.6 : volatility_high ? 2.2 : 2.8
volatility_adjustment = enable_volatility_adaptive ? (atr_pct / 3.0) : 0
atr_multiplier = base_atr_multiplier + volatility_adjustment
atr_stop_distance = atr * atr_multiplier

// Structure-based stop loss with multiple levels
long_stop_loss = entry_price - atr_stop_distance
if not na(last_swing_low)
    structure_stop = last_swing_low * 0.998
    long_stop_loss := math.max(long_stop_loss, structure_stop)

short_stop_loss = entry_price + atr_stop_distance
if not na(last_swing_high)
    structure_stop = last_swing_high * 1.002
    short_stop_loss := math.min(short_stop_loss, structure_stop)

// Enhanced Take Profit with dynamic R:R based on signal quality
base_rr_multiplier = long_signal_quality == "Premium" or short_signal_quality == "Premium" ? 2.5 :
                     long_signal_quality == "High" or short_signal_quality == "High" ? 2.2 :
                     long_signal_quality == "Good" or short_signal_quality == "Good" ? 2.0 : 1.8

long_tp1 = entry_price + (entry_price - long_stop_loss) * base_rr_multiplier
long_tp2 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 1.0)
long_tp3 = entry_price + (entry_price - long_stop_loss) * (base_rr_multiplier + 2.5)

short_tp1 = entry_price - (short_stop_loss - entry_price) * base_rr_multiplier
short_tp2 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 1.0)
short_tp3 = entry_price - (short_stop_loss - entry_price) * (base_rr_multiplier + 2.5)

// Risk:Reward validation
long_rr_ratio = (long_tp1 - entry_price) / (entry_price - long_stop_loss)
short_rr_ratio = (entry_price - short_tp1) / (short_stop_loss - entry_price)

long_valid_rr = long_rr_ratio >= risk_reward_min
short_valid_rr = short_rr_ratio >= risk_reward_min

// Final signal validation
final_long_signal = long_signal_final and long_valid_rr
final_short_signal = short_signal_final and short_valid_rr

// ============================================================================
// CLEAN VISUAL ELEMENTS
// ============================================================================

// Plot EMAs with enhanced styling
plot(ema_f, "EMA Fast", color=show_ema_lines ? color.new(color.blue, 40) : na, linewidth=1)
plot(ema_m, "EMA Medium", color=show_ema_lines ? color.new(color.orange, 30) : na, linewidth=2)
plot(ema_s, "EMA Slow", color=show_ema_lines ? color.new(color.red, 40) : na, linewidth=2)
plot(ema_t, "EMA Trend", color=show_ema_lines ? color.new(color.purple, 50) : na, linewidth=2)

// Plot Bollinger Bands
p1 = plot(bb_upper, "BB Upper", color=show_bb_lines ? color.new(color.gray, 70) : na, linewidth=1)
p2 = plot(bb_lower, "BB Lower", color=show_bb_lines ? color.new(color.gray, 70) : na, linewidth=1)
fill(p1, p2, color=show_bb_lines ? color.new(color.gray, 95) : na, title="BB Fill")

// ============================================================================
// ENHANCED SIGNAL LABELS
// ============================================================================

label_size = signal_label_size == "Small" ? size.small : signal_label_size == "Large" ? size.large : size.normal

// Enhanced LONG Signal Label
if final_long_signal
    signal_text = "🟢 " + long_signal_quality + " LONG\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                  "🛑 Stop Loss: " + str.tostring(long_stop_loss, "#.####") + "\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "🎯 TP1: " + str.tostring(long_tp1, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier, "#.#") + ")\n" +
                  "🎯 TP2: " + str.tostring(long_tp2, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 1.0, "#.#") + ")\n" +
                  "🎯 TP3: " + str.tostring(long_tp3, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 2.5, "#.#") + ")\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📊 Confidence: " + str.tostring(confidence_score) + "%\n" +
                  "🔥 Quality: " + long_signal_quality + "\n" +
                  "📈 Trend: " + str.tostring(trend_strength)

    label_color = long_signal_quality == "Premium" ? color.new(color.lime, 5) :
                  long_signal_quality == "High" ? color.new(color.green, 5) :
                  long_signal_quality == "Good" ? color.new(color.green, 15) : color.new(color.green, 25)

    label.new(bar_index, low - atr*2, text=signal_text, style=label.style_label_up, color=label_color, textcolor=color.white, size=label_size)

// Enhanced SHORT Signal Label
if final_short_signal
    signal_text = "🔴 " + short_signal_quality + " SHORT\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                  "🛑 Stop Loss: " + str.tostring(short_stop_loss, "#.####") + "\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "🎯 TP1: " + str.tostring(short_tp1, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier, "#.#") + ")\n" +
                  "🎯 TP2: " + str.tostring(short_tp2, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 1.0, "#.#") + ")\n" +
                  "🎯 TP3: " + str.tostring(short_tp3, "#.####") + " (R:R 1:" + str.tostring(base_rr_multiplier + 2.5, "#.#") + ")\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📊 Confidence: " + str.tostring(confidence_score) + "%\n" +
                  "🔥 Quality: " + short_signal_quality + "\n" +
                  "📉 Trend: " + str.tostring(trend_strength)

    label_color = short_signal_quality == "Premium" ? color.new(color.red, 5) :
                  short_signal_quality == "High" ? color.new(color.red, 5) :
                  short_signal_quality == "Good" ? color.new(color.red, 15) : color.new(color.red, 25)

    label.new(bar_index, high + atr*2, text=signal_text, style=label.style_label_down, color=label_color, textcolor=color.white, size=label_size)

// Enhanced Confidence Badge
if show_confidence and (final_long_signal or final_short_signal)
    conf_color = confidence_score >= 85 ? color.new(color.lime, 10) :
                 confidence_score >= 75 ? color.new(color.green, 10) :
                 confidence_score >= 65 ? color.new(color.yellow, 10) : color.new(color.orange, 10)

    conf_text = "📊 " + str.tostring(confidence_score) + "%\n🎯 " + (final_long_signal ? long_signal_quality : short_signal_quality)
    badge_y = final_long_signal ? low - atr*4.5 : high + atr*4.5

    label.new(bar_index, badge_y, text=conf_text, style=label.style_label_center, color=conf_color, textcolor=color.white, size=size.small)

// ============================================================================
// ALERT CONDITIONS
// ============================================================================

alertcondition(final_long_signal, "Enhanced Long Signal", "🟢 Enhanced LONG Signal Generated")
alertcondition(final_short_signal, "Enhanced Short Signal", "🔴 Enhanced SHORT Signal Generated")
alertcondition(final_long_signal and long_signal_quality == "Premium", "Premium Long Signal", "🟢 PREMIUM LONG Signal Generated")
alertcondition(final_short_signal and short_signal_quality == "Premium", "Premium Short Signal", "🔴 PREMIUM SHORT Signal Generated")

// ============================================================================
// ENHANCED STRATEGY NOTES
// ============================================================================

// Enhanced Strategy Features:
// 1. ✅ Multi-tier signal system (Premium/High/Good/Alternative)
// 2. ✅ Enhanced confidence scoring with better distribution
// 3. ✅ Flexible signal conditions - not requiring all criteria
// 4. ✅ Alternative signal types when primary conditions not met
// 5. ✅ Dynamic risk:reward ratios based on signal quality
// 6. ✅ Volatility-adaptive stop losses
// 7. ✅ Enhanced momentum and trend strength analysis
// 8. ✅ Optimized parameters for crypto trading
// 9. ✅ Signal mode selection (Conservative/Balanced/Aggressive)
// 10. ✅ Advanced volume and market structure analysis

// Key Improvements:
// - Increased signal frequency while maintaining quality
// - Better entry timing with multiple confirmation levels
// - Enhanced risk management with dynamic adjustments
// - Reduced false signals through multi-tier validation
// - Alternative signals for additional opportunities

// Credits: Trading Signal Bot - Enhanced Strategy Version
// Optimized for high accuracy and optimal signal frequency
