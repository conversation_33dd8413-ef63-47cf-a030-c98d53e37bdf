"""
<PERSON><PERSON><PERSON> hình cho hệ thống thông báo
<PERSON> lý <PERSON>rd, <PERSON>alo và các kênh thông báo khác
"""

from typing import Dict, Any, List, Optional
from enum import Enum
from config.settings import settings

class NotificationPriority(Enum):
    """Mức độ ưu tiên thông báo"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class NotificationChannel(Enum):
    """Kênh thông báo"""
    DISCORD = "discord"
    ZALO = "zalo"
    TELEGRAM = "telegram"
    EMAIL = "email"
    WEBHOOK = "webhook"

class NotificationConfig:
    """C<PERSON>u hình hệ thống thông báo"""
    
    # =============================================================================
    # DISCORD CONFIGURATION
    # =============================================================================
    
    DISCORD_CONFIG = {
        'bot_token': settings.DISCORD_BOT_TOKEN,
        'channel_id': settings.DISCORD_CHANNEL_ID,
        'guild_id': settings.DISCORD_GUILD_ID,
        'embed_color': {
            'long': 0x00ff00,    # Green for LONG signals
            'short': 0xff0000,   # Red for SHORT signals
            'info': 0x0099ff,    # Blue for info messages
            'warning': 0xffaa00, # Orange for warnings
            'error': 0xff0000    # Red for errors
        },
        'rate_limit': {
            'messages_per_minute': 30,
            'burst_limit': 5
        }
    }
    
    # =============================================================================
    # ZALO CONFIGURATION
    # =============================================================================
    
    ZALO_CONFIG = {
        'app_id': settings.ZALO_APP_ID,
        'app_secret': settings.ZALO_APP_SECRET,
        'access_token': settings.ZALO_ACCESS_TOKEN,
        'phone_number': settings.ZALO_PHONE_NUMBER,
        'api_endpoints': {
            'send_message': 'https://openapi.zalo.me/v2.0/oa/message',
            'upload_image': 'https://openapi.zalo.me/v2.0/oa/upload/image',
            'get_profile': 'https://openapi.zalo.me/v2.0/oa/getprofile'
        },
        'rate_limit': {
            'messages_per_minute': 20,
            'burst_limit': 3
        }
    }
    
    # =============================================================================
    # MESSAGE TEMPLATES
    # =============================================================================
    
    DISCORD_TEMPLATES = {
        'signal': {
            'title': '🚨 {signal_type} Signal - {symbol}',
            'description': '{reasoning}',
            'fields': [
                {'name': '📊 Confidence Score', 'value': '{confidence_score}%', 'inline': True},
                {'name': '💰 Entry Price', 'value': '${entry_price:,.4f}', 'inline': True},
                {'name': '🛑 Stop Loss', 'value': '${stop_loss:,.4f}', 'inline': True},
                {'name': '🎯 Take Profit 1', 'value': '${tp1_price:,.4f}', 'inline': True},
                {'name': '🎯 Take Profit 2', 'value': '${tp2_price:,.4f}', 'inline': True},
                {'name': '🎯 Take Profit 3', 'value': '${tp3_price:,.4f}', 'inline': True},
                {'name': '⚖️ Risk:Reward', 'value': '1:{risk_reward_ratio:.1f}', 'inline': True},
                {'name': '📈 Position Size', 'value': '{position_size_percentage:.1f}%', 'inline': True},
                {'name': '⏰ Timeframe', 'value': '{timeframe}', 'inline': True},
                {'name': '🔥 Volatility', 'value': '{volatility}', 'inline': True},
                {'name': '📊 Volume', 'value': '{volume_profile}', 'inline': True},
                {'name': '💪 Trend Strength', 'value': '{trend_strength}/100', 'inline': True}
            ],
            'footer': 'Trading Signal Bot • {timestamp}',
            'thumbnail': 'https://cryptologos.cc/logos/{symbol_lower}-logo.png'
        },
        'market_update': {
            'title': '📈 Market Update',
            'description': '{message}',
            'color': 0x0099ff
        },
        'system_alert': {
            'title': '⚠️ System Alert',
            'description': '{message}',
            'color': 0xffaa00
        },
        'error': {
            'title': '❌ System Error',
            'description': '{error_message}',
            'color': 0xff0000
        }
    }
    
    ZALO_TEMPLATES = {
        'signal': '''🚨 {signal_type} SIGNAL - {symbol}
        
💰 Entry: ${entry_price:,.4f}
🛑 Stop Loss: ${stop_loss:,.4f}
🎯 Take Profit: ${tp1_price:,.4f}
📊 Confidence: {confidence_score}%
⚖️ R:R: 1:{risk_reward_ratio:.1f}
⏰ Timeframe: {timeframe}

📝 Lý do: {reasoning}

⚡ Gửi lúc: {timestamp}''',
        
        'market_update': '''📈 CẬP NHẬT THỊ TRƯỜNG
        
{message}

⚡ Thời gian: {timestamp}''',
        
        'system_alert': '''⚠️ CẢNH BÁO HỆ THỐNG
        
{message}

⚡ Thời gian: {timestamp}'''
    }
    
    # =============================================================================
    # NOTIFICATION RULES
    # =============================================================================
    
    NOTIFICATION_RULES = {
        'signal': {
            'channels': [NotificationChannel.DISCORD, NotificationChannel.ZALO],
            'priority': NotificationPriority.HIGH,
            'conditions': {
                'min_confidence': settings.MIN_CONFIDENCE_SCORE,
                'max_per_hour': settings.MAX_SIGNALS_PER_HOUR,
                'cooldown_minutes': 5  # Minimum time between signals for same symbol
            }
        },
        'market_update': {
            'channels': [NotificationChannel.DISCORD],
            'priority': NotificationPriority.MEDIUM,
            'conditions': {
                'max_per_hour': 6
            }
        },
        'system_alert': {
            'channels': [NotificationChannel.DISCORD, NotificationChannel.ZALO],
            'priority': NotificationPriority.HIGH,
            'conditions': {
                'max_per_hour': 10
            }
        },
        'error': {
            'channels': [NotificationChannel.DISCORD],
            'priority': NotificationPriority.CRITICAL,
            'conditions': {
                'max_per_hour': 20
            }
        }
    }
    
    # =============================================================================
    # HELPER METHODS
    # =============================================================================
    
    @staticmethod
    def get_channels_for_priority(priority: NotificationPriority) -> List[NotificationChannel]:
        """
        Lấy danh sách kênh thông báo theo mức độ ưu tiên
        
        Args:
            priority: Mức độ ưu tiên
            
        Returns:
            List[NotificationChannel]: Danh sách kênh thông báo
        """
        if priority == NotificationPriority.CRITICAL:
            return [NotificationChannel.DISCORD, NotificationChannel.ZALO]
        elif priority == NotificationPriority.HIGH:
            return [NotificationChannel.DISCORD, NotificationChannel.ZALO]
        elif priority == NotificationPriority.MEDIUM:
            return [NotificationChannel.DISCORD]
        else:
            return [NotificationChannel.DISCORD]
    
    @staticmethod
    def get_template(channel: NotificationChannel, message_type: str) -> Optional[Dict[str, Any]]:
        """
        Lấy template thông báo
        
        Args:
            channel: Kênh thông báo
            message_type: Loại thông báo
            
        Returns:
            Optional[Dict]: Template thông báo
        """
        if channel == NotificationChannel.DISCORD:
            return NotificationConfig.DISCORD_TEMPLATES.get(message_type)
        elif channel == NotificationChannel.ZALO:
            return NotificationConfig.ZALO_TEMPLATES.get(message_type)
        return None
    
    @staticmethod
    def get_rate_limit(channel: NotificationChannel) -> Dict[str, int]:
        """
        Lấy giới hạn tốc độ gửi thông báo
        
        Args:
            channel: Kênh thông báo
            
        Returns:
            Dict[str, int]: Thông tin rate limit
        """
        if channel == NotificationChannel.DISCORD:
            return NotificationConfig.DISCORD_CONFIG['rate_limit']
        elif channel == NotificationChannel.ZALO:
            return NotificationConfig.ZALO_CONFIG['rate_limit']
        return {'messages_per_minute': 10, 'burst_limit': 2}
    
    @staticmethod
    def should_send_notification(message_type: str, **kwargs) -> bool:
        """
        Kiểm tra xem có nên gửi thông báo hay không
        
        Args:
            message_type: Loại thông báo
            **kwargs: Các tham số bổ sung
            
        Returns:
            bool: True nếu nên gửi thông báo
        """
        rules = NotificationConfig.NOTIFICATION_RULES.get(message_type, {})
        conditions = rules.get('conditions', {})
        
        # Kiểm tra confidence score cho signal
        if message_type == 'signal':
            confidence = kwargs.get('confidence_score', 0)
            if confidence < conditions.get('min_confidence', 75):
                return False
        
        # Có thể thêm các điều kiện khác ở đây
        # Ví dụ: kiểm tra rate limit, cooldown, etc.
        
        return True
    
    # =============================================================================
    # FORMATTING HELPERS
    # =============================================================================
    
    @staticmethod
    def format_signal_data(signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format dữ liệu signal cho thông báo
        
        Args:
            signal_data: Dữ liệu signal raw
            
        Returns:
            Dict[str, Any]: Dữ liệu đã được format
        """
        formatted = signal_data.copy()
        
        # Format symbol
        symbol = signal_data.get('symbol', '')
        formatted['symbol_lower'] = symbol.replace('/', '').lower()
        
        # Format signal type
        signal_type = signal_data.get('signal_type', 'LONG')
        formatted['signal_type'] = '🟢 LONG' if signal_type == 'LONG' else '🔴 SHORT'
        
        # Format prices
        for price_field in ['entry_price', 'stop_loss']:
            if price_field in formatted:
                formatted[price_field] = float(formatted[price_field])
        
        # Format take profit levels
        tp_levels = signal_data.get('take_profit_levels', [])
        if tp_levels:
            formatted['tp1_price'] = float(tp_levels[0].get('price', 0))
            formatted['tp2_price'] = float(tp_levels[1].get('price', 0)) if len(tp_levels) > 1 else 0
            formatted['tp3_price'] = float(tp_levels[2].get('price', 0)) if len(tp_levels) > 2 else 0
        
        # Format timestamp
        from datetime import datetime
        formatted['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return formatted

# Global notification configuration instance
notification_config = NotificationConfig()
