//@version=5
indicator("Trading Signal Bot - Clean Professional", shorttitle="TSB-Clean", overlay=true, max_boxes_count=10, max_lines_count=10, max_labels_count=50)

// ============================================================================
// TRADING SIGNAL BOT - CLEAN PROFESSIONAL VERSION (ERROR-FREE)
// Minimal visual clutter, focus on essential trading signals only
// Based on TECHNICAL_SPECIFICATION.md enhanced strategy
// ============================================================================

// ============================================================================
// CONFIGURATION PARAMETERS (STREAMLINED)
// ============================================================================

// Technical Indicators Settings
group_ti = "📊 Technical Indicators"
ema_9 = input.int(9, "EMA 9", minval=1, group=group_ti)
ema_21 = input.int(21, "EMA 21", minval=1, group=group_ti)
ema_50 = input.int(50, "EMA 50", minval=1, group=group_ti)
ema_200 = input.int(200, "EMA 200", minval=1, group=group_ti)
rsi_length = input.int(14, "RSI Length", minval=1, group=group_ti)
macd_fast = input.int(12, "MACD Fast", minval=1, group=group_ti)
macd_slow = input.int(26, "MACD Slow", minval=1, group=group_ti)
macd_signal = input.int(9, "MACD Signal", minval=1, group=group_ti)
bb_length = input.int(20, "Bollinger Bands Length", minval=1, group=group_ti)
bb_mult = input.float(2.0, "Bollinger Bands Multiplier", minval=0.1, group=group_ti)
atr_length = input.int(14, "ATR Length", minval=1, group=group_ti)

// Signal Settings
group_sig = "🎯 Signal Generation"
min_confidence = input.int(75, "Minimum Confidence Score", minval=0, maxval=100, group=group_sig)
show_signals = input.bool(true, "Show Trading Signals", group=group_sig)
show_confidence = input.bool(true, "Show Confidence Score", group=group_sig)
risk_reward_min = input.float(2.0, "Minimum Risk:Reward Ratio", minval=1.0, group=group_sig)
structure_lookback = input.int(20, "Structure Lookback Period", minval=5, maxval=100, group=group_sig)
range_lookback = input.int(50, "Range Lookback for Premium/Discount", minval=20, maxval=200, group=group_sig)

// Visual Settings
group_vis = "🎨 Visual Settings"
signal_label_size = input.string("Normal", "Signal Label Size", options=["Small", "Normal", "Large"], group=group_vis)
show_ema_lines = input.bool(true, "Show EMA Lines", group=group_vis)
show_bb_lines = input.bool(false, "Show Bollinger Bands", group=group_vis)

// ============================================================================
// TECHNICAL INDICATORS CALCULATIONS
// ============================================================================

// Moving Averages
ema9 = ta.ema(close, ema_9)
ema21 = ta.ema(close, ema_21)
ema50 = ta.ema(close, ema_50)
ema200 = ta.ema(close, ema_200)

// RSI
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line and histogram > 0
macd_bearish = macd_line < signal_line and histogram < 0

// Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
bb_upper_break = close > bb_upper
bb_lower_break = close < bb_lower

// ATR and Volatility Classification
atr = ta.atr(atr_length)
atr_pct = (atr / close) * 100
volatility_low = atr_pct < 2
volatility_medium = atr_pct >= 2 and atr_pct < 4
volatility_high = atr_pct >= 4 and atr_pct < 6
volatility_extreme = atr_pct >= 6

// Volume Analysis
volume_avg = ta.sma(volume, 20)
volume_surge = volume > volume_avg * 2
volume_high = volume > volume_avg * 1.5

// ============================================================================
// MARKET STRUCTURE ANALYSIS (BACKGROUND CALCULATIONS)
// ============================================================================

// Swing Highs and Lows Detection
swing_high = ta.pivothigh(high, structure_lookback, structure_lookback)
swing_low = ta.pivotlow(low, structure_lookback, structure_lookback)

// Break of Structure (BOS) Detection
var float last_swing_high = na
var float last_swing_low = na

if not na(swing_high)
    last_swing_high := swing_high

if not na(swing_low)
    last_swing_low := swing_low

// BOS Detection
bos_bullish = not na(last_swing_high) and close > last_swing_high and close[1] <= last_swing_high
bos_bearish = not na(last_swing_low) and close < last_swing_low and close[1] >= last_swing_low

// Change of Character (CHoCH) Detection
var bool trend_bullish = true
choch_bearish = trend_bullish and bos_bearish
choch_bullish = not trend_bullish and bos_bullish

if choch_bullish
    trend_bullish := true
if choch_bearish
    trend_bullish := false

// Premium/Discount Zones (Background calculations)
range_high = ta.highest(high, range_lookback)
range_low = ta.lowest(low, range_lookback)
range_mid = (range_high + range_low) / 2

in_premium = close > range_mid
in_discount = close < range_mid

// ============================================================================
// CONFIDENCE SCORE CALCULATION
// ============================================================================

confidence_score = 30 // Base score

// 1. Market Structure Analysis (25 points)
structure_points = 0
if bos_bullish or bos_bearish
    structure_points += 15
if choch_bullish or choch_bearish  
    structure_points += 10
confidence_score += structure_points

// 2. Smart Money Confirmation (20 points)
smart_money_points = 0
if volume_surge
    smart_money_points += 10
if (bos_bullish and in_discount) or (bos_bearish and in_premium)
    smart_money_points += 10
confidence_score += smart_money_points

// 3. Multi-timeframe Alignment (15 points)
alignment_points = 0
ema_bullish_alignment = ema9 > ema21 and ema21 > ema50 and ema50 > ema200
ema_bearish_alignment = ema9 < ema21 and ema21 < ema50 and ema50 < ema200
if ema_bullish_alignment or ema_bearish_alignment
    alignment_points += 12
confidence_score += alignment_points

// 4. Traditional Indicators (15 points)
indicator_points = 0
if macd_bullish or macd_bearish
    indicator_points += 5
if rsi_oversold or rsi_overbought
    indicator_points += 5
if bb_upper_break or bb_lower_break
    indicator_points += 5
confidence_score += indicator_points

// 5. Market Regime Suitability (10 points)
regime_points = 0
if not volatility_extreme
    regime_points += 8
confidence_score += regime_points

// 6. Volume & Flow Analysis (10 points)
volume_points = 0
if volume_high
    volume_points += 5
if volume_surge
    volume_points += 5
confidence_score += volume_points

// 7. Risk Environment Assessment (5 points)
risk_points = 0
if volatility_low or volatility_medium
    risk_points += 3
confidence_score += risk_points

confidence_score := math.max(0, math.min(100, confidence_score))

// ============================================================================
// SIGNAL GENERATION LOGIC
// ============================================================================

// LONG Signal Conditions
long_structure = bos_bullish or (trend_bullish and not choch_bearish)
long_smart_money = in_discount and volume_high
long_technical = ema_bullish_alignment and (rsi_oversold or macd_bullish)
long_entry_trigger = close > ema21 or bb_lower_break

long_signal = show_signals and confidence_score >= min_confidence and long_structure and long_smart_money and long_technical and long_entry_trigger

// SHORT Signal Conditions
short_structure = bos_bearish or (not trend_bullish and not choch_bullish)
short_smart_money = in_premium and volume_high
short_technical = ema_bearish_alignment and (rsi_overbought or macd_bearish)
short_entry_trigger = close < ema21 or bb_upper_break

short_signal = show_signals and confidence_score >= min_confidence and short_structure and short_smart_money and short_technical and short_entry_trigger

// ============================================================================
// RISK MANAGEMENT CALCULATIONS
// ============================================================================

// Entry Price
entry_price = close

// Stop Loss Calculation (ATR-based + Structure-based)
atr_multiplier = volatility_low ? 1.5 : volatility_medium ? 2.0 : volatility_high ? 2.5 : 3.0
atr_stop_distance = atr * atr_multiplier

// Long Stop Loss
long_stop_loss = entry_price - atr_stop_distance
if not na(last_swing_low) and last_swing_low < long_stop_loss
    long_stop_loss := last_swing_low * 0.999

// Short Stop Loss
short_stop_loss = entry_price + atr_stop_distance  
if not na(last_swing_high) and last_swing_high > short_stop_loss
    short_stop_loss := last_swing_high * 1.001

// Take Profit Calculation (Multiple targets)
long_tp1 = entry_price + (entry_price - long_stop_loss) * 2.0  // 1:2 R:R
long_tp2 = entry_price + (entry_price - long_stop_loss) * 3.0  // 1:3 R:R
long_tp3 = entry_price + (entry_price - long_stop_loss) * 5.0  // 1:5 R:R

short_tp1 = entry_price - (short_stop_loss - entry_price) * 2.0 // 1:2 R:R
short_tp2 = entry_price - (short_stop_loss - entry_price) * 3.0 // 1:3 R:R
short_tp3 = entry_price - (short_stop_loss - entry_price) * 5.0 // 1:5 R:R

// Risk:Reward Ratio
long_rr_ratio = (long_tp1 - entry_price) / (entry_price - long_stop_loss)
short_rr_ratio = (entry_price - short_tp1) / (short_stop_loss - entry_price)

// Validate minimum R:R ratio
long_valid_rr = long_rr_ratio >= risk_reward_min
short_valid_rr = short_rr_ratio >= risk_reward_min

// Final signal validation
final_long_signal = long_signal and long_valid_rr
final_short_signal = short_signal and short_valid_rr

// ============================================================================
// CLEAN VISUAL ELEMENTS (ERROR-FREE)
// ============================================================================

// Plot EMAs (conditional display using ternary operator)
plot(ema9, "EMA 9", color=show_ema_lines ? color.new(color.blue, 60) : na, linewidth=1)
plot(ema21, "EMA 21", color=show_ema_lines ? color.new(color.orange, 40) : na, linewidth=1)
plot(ema50, "EMA 50", color=show_ema_lines ? color.new(color.red, 50) : na, linewidth=2)
plot(ema200, "EMA 200", color=show_ema_lines ? color.new(color.purple, 60) : na, linewidth=2)

// Plot Bollinger Bands (conditional display)
p1 = plot(bb_upper, "BB Upper", color=show_bb_lines ? color.new(color.gray, 80) : na, linewidth=1)
p2 = plot(bb_lower, "BB Lower", color=show_bb_lines ? color.new(color.gray, 80) : na, linewidth=1)
fill(p1, p2, color=show_bb_lines ? color.new(color.gray, 95) : na, title="BB Fill")

// ============================================================================
// PROFESSIONAL SIGNAL LABELS (COMPREHENSIVE TRADE INFORMATION)
// ============================================================================

// Determine label size
label_size = signal_label_size == "Small" ? size.small : signal_label_size == "Large" ? size.large : size.normal

// LONG Signal with complete trade setup
if final_long_signal
    signal_text = "🟢 LONG SIGNAL\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                  "🛑 Stop Loss: " + str.tostring(long_stop_loss, "#.####") + "\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "🎯 TP1: " + str.tostring(long_tp1, "#.####") + " (R:R 1:2)\n" +
                  "🎯 TP2: " + str.tostring(long_tp2, "#.####") + " (R:R 1:3)\n" +
                  "🎯 TP3: " + str.tostring(long_tp3, "#.####") + " (R:R 1:5)\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📊 Confidence: " + str.tostring(confidence_score) + "%\n" +
                  "⚖️ Risk:Reward: 1:" + str.tostring(long_rr_ratio, "#.#")

    label.new(bar_index, low - atr*2, text=signal_text, style=label.style_label_up, color=color.new(color.green, 5), textcolor=color.white, size=label_size, tooltip="LONG Signal - Entry: " + str.tostring(entry_price, "#.####"))

// SHORT Signal with complete trade setup
if final_short_signal
    signal_text = "🔴 SHORT SIGNAL\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                  "🛑 Stop Loss: " + str.tostring(short_stop_loss, "#.####") + "\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "🎯 TP1: " + str.tostring(short_tp1, "#.####") + " (R:R 1:2)\n" +
                  "🎯 TP2: " + str.tostring(short_tp2, "#.####") + " (R:R 1:3)\n" +
                  "🎯 TP3: " + str.tostring(short_tp3, "#.####") + " (R:R 1:5)\n" +
                  "━━━━━━━━━━━━━━━━━━━━\n" +
                  "📊 Confidence: " + str.tostring(confidence_score) + "%\n" +
                  "⚖️ Risk:Reward: 1:" + str.tostring(short_rr_ratio, "#.#")

    label.new(bar_index, high + atr*2, text=signal_text, style=label.style_label_down, color=color.new(color.red, 5), textcolor=color.white, size=label_size, tooltip="SHORT Signal - Entry: " + str.tostring(entry_price, "#.####"))

// Confidence Score Badge (appears only with signals)
if show_confidence and (final_long_signal or final_short_signal)
    conf_color = confidence_score >= 90 ? color.new(color.lime, 15) : confidence_score >= 85 ? color.new(color.green, 15) : confidence_score >= 80 ? color.new(color.yellow, 15) : confidence_score >= 75 ? color.new(color.orange, 15) : color.new(color.red, 15)

    conf_text = "📊 CONFIDENCE\n" + str.tostring(confidence_score) + "%"
    badge_y = final_long_signal ? low - atr*4.5 : high + atr*4.5

    label.new(bar_index, badge_y, text=conf_text, style=label.style_label_center, color=conf_color, textcolor=color.white, size=size.small)

// ============================================================================
// ALERT CONDITIONS (MAINTAINED FOR NOTIFICATIONS)
// ============================================================================

alertcondition(final_long_signal, "Long Signal", "🟢 LONG Signal Generated")
alertcondition(final_short_signal, "Short Signal", "🔴 SHORT Signal Generated")

// ============================================================================
// NOTES - CLEAN PROFESSIONAL VERSION
// ============================================================================

// Clean Professional Features:
// 1. ✅ Minimal visual clutter - focus on essential signals only
// 2. ✅ Comprehensive signal labels with complete trade information
// 3. ✅ Professional presentation optimized for decision-making
// 4. ✅ Optional EMA lines for trend reference (subtle styling)
// 5. ✅ Confidence-based filtering (minimum 75%)
// 6. ✅ Multiple take profit levels (1:2, 1:3, 1:5 R:R)
// 7. ✅ Clean, readable labels without chart clutter
// 8. ✅ Alert functionality maintained for notifications
// 9. ✅ Error-free code compatible with Pine Script v5

// Usage Instructions:
// - Enable "Show EMA Lines" for trend reference
// - Adjust "Signal Label Size" for readability preference
// - Set "Minimum Confidence Score" to filter signal quality (recommend 85%)
// - Use alerts for real-time notifications
// - Focus on signals with confidence ≥ 85% for highest probability

// Credits: Trading Signal Bot - Clean Professional Version (Error-Free)
// Optimized for professional traders who prefer minimal visual distractions
