//@version=5
indicator("Trading Signal Bot - Advanced Market Structure", shorttitle="TSB-AMS", overlay=true, max_boxes_count=100, max_lines_count=100, max_labels_count=100)

// ============================================================================
// TRADING SIGNAL BOT - ADVANCED MARKET STRUCTURE INDICATOR
// Dựa trên TECHNICAL_SPECIFICATION.md đã được cải tiến
// Tích hợp Market Structure Analysis + Smart Money Concepts + Advanced Signals
// ============================================================================

// ============================================================================
// CÁC THAM SỐ CẤU HÌNH
// ============================================================================

// Market Structure Settings
group_ms = "🏗️ Market Structure Analysis"
enable_bos = input.bool(false, "Enable Break of Structure (BOS)", group=group_ms)
enable_choch = input.bool(false, "Enable Change of Character (CHoCH)", group=group_ms)
enable_ob = input.bool(false, "Enable Order Blocks", group=group_ms)
enable_fvg = input.bool(false, "Enable Fair Value Gaps", group=group_ms)
structure_lookback = input.int(20, "Structure Lookback Period", minval=5, maxval=100, group=group_ms)

// Smart Money Settings
group_sm = "💰 Smart Money Concepts"
enable_premium_discount = input.bool(false, "Enable Premium/Discount Zones", group=group_sm)
range_lookback = input.int(50, "Range Lookback for Premium/Discount", minval=20, maxval=200, group=group_sm)

// Technical Indicators Settings
group_ti = "📊 Technical Indicators"
ema_9 = input.int(9, "EMA 9", minval=1, group=group_ti)
ema_21 = input.int(21, "EMA 21", minval=1, group=group_ti)
ema_50 = input.int(50, "EMA 50", minval=1, group=group_ti)
ema_200 = input.int(200, "EMA 200", minval=1, group=group_ti)
rsi_length = input.int(14, "RSI Length", minval=1, group=group_ti)
macd_fast = input.int(12, "MACD Fast", minval=1, group=group_ti)
macd_slow = input.int(26, "MACD Slow", minval=1, group=group_ti)
macd_signal = input.int(9, "MACD Signal", minval=1, group=group_ti)
bb_length = input.int(20, "Bollinger Bands Length", minval=1, group=group_ti)
bb_mult = input.float(2.0, "Bollinger Bands Multiplier", minval=0.1, group=group_ti)
atr_length = input.int(14, "ATR Length", minval=1, group=group_ti)

// Signal Settings
group_sig = "🎯 Signal Generation"
min_confidence = input.int(75, "Minimum Confidence Score", minval=0, maxval=100, group=group_sig)
show_signals = input.bool(true, "Show Trading Signals", group=group_sig)
show_confidence = input.bool(true, "Show Confidence Score", group=group_sig)
risk_reward_min = input.float(2.0, "Minimum Risk:Reward Ratio", minval=1.0, group=group_sig)
show_info_table = input.bool(false, "Show Information Table", group=group_sig)

// Visual Settings
group_vis = "🎨 Visual Settings"
signal_label_size = input.string("Normal", "Signal Label Size", options=["Small", "Normal", "Large"], group=group_vis)
show_ema_lines = input.bool(true, "Show EMA Lines", group=group_vis)

// ============================================================================
// TÍNH TOÁN CÁC CHỈ BÁO KỸ THUẬT
// ============================================================================

// Moving Averages
ema9 = ta.ema(close, ema_9)
ema21 = ta.ema(close, ema_21)
ema50 = ta.ema(close, ema_50)
ema200 = ta.ema(close, ema_200)

// RSI
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70

// MACD
[macd_line, signal_line, histogram] = ta.macd(close, macd_fast, macd_slow, macd_signal)
macd_bullish = macd_line > signal_line and histogram > 0
macd_bearish = macd_line < signal_line and histogram < 0

// Bollinger Bands
[bb_upper, bb_middle, bb_lower] = ta.bb(close, bb_length, bb_mult)
bb_squeeze = (bb_upper - bb_lower) / bb_middle < 0.1
bb_upper_break = close > bb_upper
bb_lower_break = close < bb_lower

// ATR và Volatility Classification
atr = ta.atr(atr_length)
atr_pct = (atr / close) * 100
volatility_low = atr_pct < 2
volatility_medium = atr_pct >= 2 and atr_pct < 4
volatility_high = atr_pct >= 4 and atr_pct < 6
volatility_extreme = atr_pct >= 6

// Volume Analysis
volume_avg = ta.sma(volume, 20)
volume_surge = volume > volume_avg * 2
volume_high = volume > volume_avg * 1.5

// ============================================================================
// MARKET STRUCTURE ANALYSIS
// ============================================================================

// Swing Highs và Lows Detection
swing_high = ta.pivothigh(high, structure_lookback, structure_lookback)
swing_low = ta.pivotlow(low, structure_lookback, structure_lookback)

// Break of Structure (BOS) Detection
var float last_swing_high = na
var float last_swing_low = na

if not na(swing_high)
    last_swing_high := swing_high

if not na(swing_low)
    last_swing_low := swing_low

// BOS Detection
bos_bullish = not na(last_swing_high) and close > last_swing_high and close[1] <= last_swing_high
bos_bearish = not na(last_swing_low) and close < last_swing_low and close[1] >= last_swing_low

// Change of Character (CHoCH) Detection
var bool trend_bullish = true
choch_bearish = trend_bullish and bos_bearish
choch_bullish = not trend_bullish and bos_bullish

if choch_bullish
    trend_bullish := true
if choch_bearish
    trend_bullish := false

// ============================================================================
// ORDER BLOCKS DETECTION
// ============================================================================

if bos_bullish and enable_ob
    ob_candle_index = 0
    for i = 1 to 10
        if close[i] > open[i]
            ob_candle_index := i
            break
    
    if ob_candle_index > 0
        ob_top = high[ob_candle_index]
        ob_bottom = low[ob_candle_index]
        ob_left = bar_index - ob_candle_index
        ob_right = bar_index + 20
        
        box.new(ob_left, ob_top, ob_right, ob_bottom, border_color=color.green, bgcolor=color.new(color.green, ob_transparency), text="OB Bull")

if bos_bearish and enable_ob
    ob_candle_index = 0
    for i = 1 to 10
        if close[i] < open[i]
            ob_candle_index := i
            break
    
    if ob_candle_index > 0
        ob_top = high[ob_candle_index]
        ob_bottom = low[ob_candle_index]
        ob_left = bar_index - ob_candle_index
        ob_right = bar_index + 20
        
        box.new(ob_left, ob_top, ob_right, ob_bottom, border_color=color.red, bgcolor=color.new(color.red, ob_transparency), text="OB Bear")

// ============================================================================
// FAIR VALUE GAPS (FVG) DETECTION
// ============================================================================

bullish_fvg = enable_fvg and low > high[2] and close > close[1] and close[1] > close[2]
bearish_fvg = enable_fvg and high < low[2] and close < close[1] and close[1] < close[2]

if bullish_fvg
    fvg_top = low
    fvg_bottom = high[2]
    box.new(bar_index-2, fvg_top, bar_index+10, fvg_bottom, border_color=color.blue, bgcolor=color.new(color.blue, fvg_transparency), text="FVG↑")

if bearish_fvg
    fvg_top = low[2]
    fvg_bottom = high
    box.new(bar_index-2, fvg_top, bar_index+10, fvg_bottom, border_color=color.orange, bgcolor=color.new(color.orange, fvg_transparency), text="FVG↓")

// ============================================================================
// PREMIUM/DISCOUNT ZONES
// ============================================================================

range_high = ta.highest(high, range_lookback)
range_low = ta.lowest(low, range_lookback)
range_mid = (range_high + range_low) / 2

premium_zone_bottom = range_mid
premium_zone_top = range_high
discount_zone_top = range_mid
discount_zone_bottom = range_low

in_premium = close > range_mid
in_discount = close < range_mid
in_equilibrium = math.abs(close - range_mid) / (range_high - range_low) < 0.1

// ============================================================================
// CONFIDENCE SCORE CALCULATION
// ============================================================================

confidence_score = 30

// 1. Market Structure Analysis (25 points)
structure_points = 0
if bos_bullish or bos_bearish
    structure_points += 15
if choch_bullish or choch_bearish  
    structure_points += 10
confidence_score += structure_points

// 2. Smart Money Confirmation (20 points)
smart_money_points = 0
if volume_surge
    smart_money_points += 10
if (bos_bullish and in_discount) or (bos_bearish and in_premium)
    smart_money_points += 10
confidence_score += smart_money_points

// 3. Multi-timeframe Alignment (15 points)
alignment_points = 0
ema_bullish_alignment = ema9 > ema21 and ema21 > ema50 and ema50 > ema200
ema_bearish_alignment = ema9 < ema21 and ema21 < ema50 and ema50 < ema200
if ema_bullish_alignment or ema_bearish_alignment
    alignment_points += 12
confidence_score += alignment_points

// 4. Traditional Indicators (15 points)
indicator_points = 0
if macd_bullish or macd_bearish
    indicator_points += 5
if rsi_oversold or rsi_overbought
    indicator_points += 5
if bb_upper_break or bb_lower_break
    indicator_points += 5
confidence_score += indicator_points

// 5. Market Regime Suitability (10 points)
regime_points = 0
if not volatility_extreme
    regime_points += 8
confidence_score += regime_points

// 6. Volume & Flow Analysis (10 points)
volume_points = 0
if volume_high
    volume_points += 5
if volume_surge
    volume_points += 5
confidence_score += volume_points

// 7. Risk Environment Assessment (5 points)
risk_points = 0
if volatility_low or volatility_medium
    risk_points += 3
confidence_score += risk_points

confidence_score := math.max(0, math.min(100, confidence_score))

// ============================================================================
// SIGNAL GENERATION LOGIC
// ============================================================================

long_structure = bos_bullish or (trend_bullish and not choch_bearish)
long_smart_money = in_discount and volume_high
long_technical = ema_bullish_alignment and (rsi_oversold or macd_bullish)
long_entry_trigger = close > ema21 or bb_lower_break

long_signal = show_signals and confidence_score >= min_confidence and long_structure and long_smart_money and long_technical and long_entry_trigger

short_structure = bos_bearish or (not trend_bullish and not choch_bullish)
short_smart_money = in_premium and volume_high
short_technical = ema_bearish_alignment and (rsi_overbought or macd_bearish)
short_entry_trigger = close < ema21 or bb_upper_break

short_signal = show_signals and confidence_score >= min_confidence and short_structure and short_smart_money and short_technical and short_entry_trigger

// ============================================================================
// RISK MANAGEMENT CALCULATIONS
// ============================================================================

entry_price = close
atr_multiplier = volatility_low ? 1.5 : volatility_medium ? 2.0 : volatility_high ? 2.5 : 3.0
atr_stop_distance = atr * atr_multiplier

long_stop_loss = entry_price - atr_stop_distance
if not na(last_swing_low) and last_swing_low < long_stop_loss
    long_stop_loss := last_swing_low * 0.999

short_stop_loss = entry_price + atr_stop_distance  
if not na(last_swing_high) and last_swing_high > short_stop_loss
    short_stop_loss := last_swing_high * 1.001

// Take Profit Calculation (Multiple targets)
long_tp1 = entry_price + (entry_price - long_stop_loss) * 2.0
long_tp2 = entry_price + (entry_price - long_stop_loss) * 3.0
long_tp3 = entry_price + (entry_price - long_stop_loss) * 5.0

short_tp1 = entry_price - (short_stop_loss - entry_price) * 2.0
short_tp2 = entry_price - (short_stop_loss - entry_price) * 3.0
short_tp3 = entry_price - (short_stop_loss - entry_price) * 5.0

long_rr_ratio = (long_tp1 - entry_price) / (entry_price - long_stop_loss)
short_rr_ratio = (entry_price - short_tp1) / (short_stop_loss - entry_price)

long_valid_rr = long_rr_ratio >= risk_reward_min
short_valid_rr = short_rr_ratio >= risk_reward_min

final_long_signal = long_signal and long_valid_rr
final_short_signal = short_signal and short_valid_rr

// ============================================================================
// VISUAL ELEMENTS - PLOTTING (MINIMAL CLEAN VERSION)
// ============================================================================

// Plot EMAs (only if enabled)
if show_ema_lines
    plot(ema9, "EMA 9", color=color.new(color.blue, 30), linewidth=1)
    plot(ema21, "EMA 21", color=color.new(color.orange, 20), linewidth=2)
    plot(ema50, "EMA 50", color=color.new(color.red, 30), linewidth=2)
    plot(ema200, "EMA 200", color=color.new(color.purple, 40), linewidth=2)

// Note: All visual noise elements (Order Blocks, FVG, Premium/Discount zones, BOS/CHoCH markers) are removed for clean presentation

// ============================================================================
// CLEAN SIGNAL ARROWS & LABELS (OPTIMIZED FOR READABILITY)
// ============================================================================

// Determine label size
label_size = signal_label_size == "Small" ? size.small : signal_label_size == "Large" ? size.large : size.normal

// LONG Signal with comprehensive trade information
if final_long_signal
    signal_text = "🟢 LONG SIGNAL\n" +
                  "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                  "🛑 SL: " + str.tostring(long_stop_loss, "#.####") + "\n" +
                  "🎯 TP1: " + str.tostring(long_tp1, "#.####") + " (1:2)\n" +
                  "🎯 TP2: " + str.tostring(long_tp2, "#.####") + " (1:3)\n" +
                  "🎯 TP3: " + str.tostring(long_tp3, "#.####") + " (1:5)\n" +
                  "⚖️ R:R: 1:" + str.tostring(long_rr_ratio, "#.#") + "\n" +
                  "📊 Confidence: " + str.tostring(confidence_score) + "%"

    label.new(bar_index, low - atr*1.5, text=signal_text, style=label.style_label_up,
              color=color.new(color.green, 10), textcolor=color.white, size=label_size)

// SHORT Signal with comprehensive trade information
if final_short_signal
    signal_text = "🔴 SHORT SIGNAL\n" +
                  "📍 Entry: " + str.tostring(entry_price, "#.####") + "\n" +
                  "🛑 SL: " + str.tostring(short_stop_loss, "#.####") + "\n" +
                  "🎯 TP1: " + str.tostring(short_tp1, "#.####") + " (1:2)\n" +
                  "🎯 TP2: " + str.tostring(short_tp2, "#.####") + " (1:3)\n" +
                  "🎯 TP3: " + str.tostring(short_tp3, "#.####") + " (1:5)\n" +
                  "⚖️ R:R: 1:" + str.tostring(short_rr_ratio, "#.#") + "\n" +
                  "📊 Confidence: " + str.tostring(confidence_score) + "%"

    label.new(bar_index, high + atr*1.5, text=signal_text, style=label.style_label_down,
              color=color.new(color.red, 10), textcolor=color.white, size=label_size)

// Confidence Score Badge (only when signals appear)
if show_confidence and (final_long_signal or final_short_signal)
    conf_color = confidence_score >= 90 ? color.new(color.lime, 20) :
                 confidence_score >= 85 ? color.new(color.green, 20) :
                 confidence_score >= 80 ? color.new(color.yellow, 20) :
                 confidence_score >= 75 ? color.new(color.orange, 20) : color.new(color.red, 20)

    badge_y = final_long_signal ? low - atr*3 : high + atr*3
    label.new(bar_index, badge_y, text="📊 " + str.tostring(confidence_score) + "%",
              style=label.style_label_center, color=conf_color, textcolor=color.white, size=size.small)

// ============================================================================
// ALERTS SETUP
// ============================================================================

alertcondition(final_long_signal, "Long Signal", "🟢 LONG Signal Generated")
alertcondition(final_short_signal, "Short Signal", "🔴 SHORT Signal Generated")
alertcondition(bos_bullish, "BOS Bullish", "📈 Bullish Break of Structure")
alertcondition(bos_bearish, "BOS Bearish", "📉 Bearish Break of Structure")
alertcondition(choch_bullish, "CHoCH Bullish", "🔄 Bullish Change of Character")
alertcondition(choch_bearish, "CHoCH Bearish", "🔄 Bearish Change of Character")

// ============================================================================
// OPTIONAL INFORMATION TABLE (DISABLED BY DEFAULT FOR CLEAN PRESENTATION)
// ============================================================================

// Information table is now optional and disabled by default to maintain clean chart presentation
if barstate.islast and show_info_table and show_confidence
    var table info_table = table.new(position.top_right, 2, 4, bgcolor=color.new(color.white, 90), border_width=1, border_color=color.gray)

    table.cell(info_table, 0, 0, "Metric", text_color=color.black, text_size=size.tiny, bgcolor=color.new(color.blue, 70))
    table.cell(info_table, 1, 0, "Value", text_color=color.black, text_size=size.tiny, bgcolor=color.new(color.blue, 70))

    table.cell(info_table, 0, 1, "Trend", text_color=color.black, text_size=size.tiny)
    table.cell(info_table, 1, 1, trend_bullish ? "Bullish" : "Bearish", text_color=trend_bullish ? color.green : color.red, text_size=size.tiny)

    table.cell(info_table, 0, 2, "Volatility", text_color=color.black, text_size=size.tiny)
    vol_text = volatility_extreme ? "Extreme" : volatility_high ? "High" : volatility_medium ? "Medium" : "Low"
    vol_color = volatility_extreme ? color.red : volatility_high ? color.orange : volatility_medium ? color.yellow : color.green
    table.cell(info_table, 1, 2, vol_text, text_color=vol_color, text_size=size.tiny)

    table.cell(info_table, 0, 3, "Confidence", text_color=color.black, text_size=size.tiny)
    conf_color_table = confidence_score >= 90 ? color.lime : confidence_score >= 80 ? color.green : confidence_score >= 75 ? color.yellow : confidence_score >= 60 ? color.orange : color.red
    table.cell(info_table, 1, 3, str.tostring(confidence_score) + "%", text_color=conf_color_table, text_size=size.tiny)

// ============================================================================
// NOTES VÀ CREDITS - CLEAN VERSION
// ============================================================================

// Clean Indicator Features:
// 1. Minimal visual clutter - chỉ hiển thị essential trading signals
// 2. Comprehensive signal labels với entry, SL, và multiple TP levels
// 3. Confidence-based signal filtering (minimum 75%)
// 4. Professional chart presentation focused on actionable signals
// 5. Optional EMA lines for trend reference
// 6. Clean, readable signal labels với complete trade information
// 7. Optimized for decision-making without visual distractions

// Credits: Trading Signal Bot - Clean Professional Version
// Based on TECHNICAL_SPECIFICATION.md - Enhanced Strategy
// Optimized for clean, professional trading signal display
