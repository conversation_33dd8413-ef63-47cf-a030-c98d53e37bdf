"""
Helper utilities cho Trading Signal Bot
Các hàm tiện ích chung được sử dụng trong toàn bộ hệ thống
"""

import asyncio
import time
import hashlib
import json
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from decimal import Decimal, ROUND_HALF_UP
import pandas as pd
import numpy as np
from config.settings import settings

def get_current_timestamp() -> int:
    """Lấy timestamp hiện tại (milliseconds)"""
    return int(time.time() * 1000)

def get_current_datetime() -> datetime:
    """Lấy datetime hiện tại với timezone"""
    return datetime.now(settings.timezone_obj)

def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    Format datetime thành string
    
    Args:
        dt: Datetime object
        format_str: Format string
        
    Returns:
        str: Formatted datetime string
    """
    return dt.strftime(format_str)

def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """
    Parse datetime string thành datetime object
    
    Args:
        dt_str: Datetime string
        format_str: Format string
        
    Returns:
        datetime: Parsed datetime object
    """
    return datetime.strptime(dt_str, format_str).replace(tzinfo=settings.timezone_obj)

def round_price(price: float, decimals: int = 4) -> float:
    """
    Round price với số decimal places cụ thể
    
    Args:
        price: Price to round
        decimals: Number of decimal places
        
    Returns:
        float: Rounded price
    """
    if price is None or np.isnan(price):
        return 0.0
    
    decimal_price = Decimal(str(price))
    rounded = decimal_price.quantize(
        Decimal('0.' + '0' * decimals),
        rounding=ROUND_HALF_UP
    )
    return float(rounded)

def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    Tính phần trăm thay đổi
    
    Args:
        old_value: Giá trị cũ
        new_value: Giá trị mới
        
    Returns:
        float: Phần trăm thay đổi
    """
    if old_value == 0:
        return 0.0
    return ((new_value - old_value) / old_value) * 100

def normalize_symbol(symbol: str) -> str:
    """
    Normalize trading pair symbol
    
    Args:
        symbol: Raw symbol (e.g., "BTCUSDT", "BTC/USDT")
        
    Returns:
        str: Normalized symbol (e.g., "BTC/USDT")
    """
    symbol = symbol.upper().strip()
    
    if '/' in symbol:
        return symbol
    
    # Common USDT pairs
    if symbol.endswith('USDT'):
        base = symbol[:-4]
        return f"{base}/USDT"
    
    # Add more normalization rules as needed
    return symbol

def validate_trading_pair(symbol: str) -> bool:
    """
    Validate trading pair format
    
    Args:
        symbol: Trading pair symbol
        
    Returns:
        bool: True if valid
    """
    try:
        normalized = normalize_symbol(symbol)
        parts = normalized.split('/')
        return len(parts) == 2 and all(len(part) >= 2 for part in parts)
    except:
        return False

def calculate_position_size(
    account_balance: float,
    risk_percentage: float,
    entry_price: float,
    stop_loss_price: float
) -> float:
    """
    Tính position size dựa trên risk management
    
    Args:
        account_balance: Số dư tài khoản
        risk_percentage: Phần trăm rủi ro (1-5%)
        entry_price: Giá entry
        stop_loss_price: Giá stop loss
        
    Returns:
        float: Position size
    """
    if entry_price <= 0 or stop_loss_price <= 0:
        return 0.0
    
    risk_amount = account_balance * (risk_percentage / 100)
    price_difference = abs(entry_price - stop_loss_price)
    
    if price_difference == 0:
        return 0.0
    
    position_size = risk_amount / price_difference
    return round_price(position_size, 6)

def calculate_risk_reward_ratio(
    entry_price: float,
    stop_loss_price: float,
    take_profit_price: float
) -> float:
    """
    Tính tỷ lệ Risk:Reward
    
    Args:
        entry_price: Giá entry
        stop_loss_price: Giá stop loss
        take_profit_price: Giá take profit
        
    Returns:
        float: Risk:Reward ratio
    """
    if entry_price <= 0 or stop_loss_price <= 0 or take_profit_price <= 0:
        return 0.0
    
    risk = abs(entry_price - stop_loss_price)
    reward = abs(take_profit_price - entry_price)
    
    if risk == 0:
        return 0.0
    
    return reward / risk

def generate_signal_id() -> str:
    """
    Generate unique signal ID
    
    Returns:
        str: Unique signal ID
    """
    timestamp = get_current_timestamp()
    random_str = str(hash(timestamp))
    return hashlib.md5(f"{timestamp}{random_str}".encode()).hexdigest()[:16]

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safe division với default value
    
    Args:
        numerator: Tử số
        denominator: Mẫu số
        default: Giá trị default nếu chia cho 0
        
    Returns:
        float: Kết quả chia hoặc default value
    """
    if denominator == 0 or np.isnan(denominator) or np.isinf(denominator):
        return default
    return numerator / denominator

def is_market_hours() -> bool:
    """
    Kiểm tra xem có phải giờ giao dịch không
    Crypto market hoạt động 24/7 nhưng có thể có maintenance windows
    
    Returns:
        bool: True nếu đang trong giờ giao dịch
    """
    # Crypto markets are 24/7, but we can add maintenance windows here
    current_time = get_current_datetime()
    
    # Example: Avoid trading during typical maintenance hours (2-4 AM UTC)
    utc_time = current_time.astimezone(timezone.utc)
    if 2 <= utc_time.hour < 4:
        return False
    
    return True

def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator cho async retry logic
    
    Args:
        max_retries: Số lần retry tối đa
        delay: Delay ban đầu (seconds)
        backoff: Backoff multiplier
    """
    def decorator(func: Callable):
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
            
            raise last_exception
        return wrapper
    return decorator

def validate_ohlcv_data(df: pd.DataFrame) -> bool:
    """
    Validate OHLCV DataFrame
    
    Args:
        df: OHLCV DataFrame
        
    Returns:
        bool: True if valid
    """
    required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    
    if not all(col in df.columns for col in required_columns):
        return False
    
    if df.empty:
        return False
    
    # Check for negative prices
    price_columns = ['open', 'high', 'low', 'close']
    if (df[price_columns] < 0).any().any():
        return False
    
    # Check high >= low
    if (df['high'] < df['low']).any():
        return False
    
    # Check volume >= 0
    if (df['volume'] < 0).any():
        return False
    
    return True

def clean_ohlcv_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean và validate OHLCV data
    
    Args:
        df: Raw OHLCV DataFrame
        
    Returns:
        pd.DataFrame: Cleaned DataFrame
    """
    if df.empty:
        return df
    
    # Remove duplicates
    df = df.drop_duplicates(subset=['timestamp'])
    
    # Sort by timestamp
    df = df.sort_values('timestamp')
    
    # Forward fill missing values
    df = df.fillna(method='ffill')
    
    # Remove rows with invalid data
    price_columns = ['open', 'high', 'low', 'close']
    df = df[df[price_columns].gt(0).all(axis=1)]
    df = df[df['high'] >= df['low']]
    df = df[df['volume'] >= 0]
    
    return df.reset_index(drop=True)

def format_number(number: float, decimals: int = 2) -> str:
    """
    Format number với thousand separators
    
    Args:
        number: Number to format
        decimals: Decimal places
        
    Returns:
        str: Formatted number string
    """
    if number is None or np.isnan(number):
        return "0"
    
    return f"{number:,.{decimals}f}"

def truncate_string(text: str, max_length: int = 100) -> str:
    """
    Truncate string với ellipsis
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        
    Returns:
        str: Truncated string
    """
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def deep_merge_dict(dict1: Dict, dict2: Dict) -> Dict:
    """
    Deep merge two dictionaries
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary
        
    Returns:
        Dict: Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dict(result[key], value)
        else:
            result[key] = value
    
    return result

def get_memory_usage() -> Dict[str, float]:
    """
    Lấy thông tin memory usage
    
    Returns:
        Dict: Memory usage information
    """
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
        'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
        'percent': process.memory_percent()
    }
