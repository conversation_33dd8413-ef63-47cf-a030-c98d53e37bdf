#!/usr/bin/env python3
"""
Setup script cho Trading Signal Bot
Tự động hóa quá trình cài đặt và cấu hình
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Kiểm tra Python version"""
    if sys.version_info < (3, 9):
        print("❌ Error: Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def create_directories():
    """T<PERSON><PERSON> c<PERSON>c thư mục cần thiết"""
    directories = [
        'logs',
        'data/historical',
        'data/cache',
        'backtest/reports'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def install_requirements():
    """Cài đặt Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ Python dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

def setup_environment_file():
    """Setup .env file từ template"""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your API keys and configuration")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ .env.example file not found")

def check_redis():
    """Kiểm tra Redis installation"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis connection successful")
    except ImportError:
        print("⚠️  Redis Python client not installed (will be installed with requirements)")
    except redis.ConnectionError:
        print("⚠️  Redis server not running. Please install and start Redis:")
        print("   Windows: choco install redis-64")
        print("   Ubuntu: sudo apt-get install redis-server")
        print("   macOS: brew install redis")

def setup_git_hooks():
    """Setup Git pre-commit hooks"""
    if Path('.git').exists():
        try:
            subprocess.check_call(['pre-commit', 'install'])
            print("✅ Git pre-commit hooks installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  pre-commit not available (optional)")

def create_sample_config():
    """Tạo sample configuration files"""
    
    # Sample pytest.ini
    pytest_ini = """[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short --strict-markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
"""
    
    with open('pytest.ini', 'w') as f:
        f.write(pytest_ini)
    print("✅ Created pytest.ini")
    
    # Sample .gitignore
    gitignore = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Data
data/historical/
data/cache/
*.db
*.sqlite

# Backtest results
backtest/reports/

# OS
.DS_Store
Thumbs.db

# Redis
dump.rdb

# Jupyter
.ipynb_checkpoints/

# Coverage
htmlcov/
.coverage
.coverage.*
coverage.xml
"""
    
    if not Path('.gitignore').exists():
        with open('.gitignore', 'w') as f:
            f.write(gitignore)
        print("✅ Created .gitignore")

def run_initial_tests():
    """Chạy basic tests để verify setup"""
    print("🧪 Running initial tests...")
    
    try:
        # Test imports
        import pandas
        import numpy
        import ccxt
        print("✅ Core libraries import successfully")
        
        # Test configuration
        from config.settings import settings
        print(f"✅ Configuration loaded - App: {settings.APP_NAME}")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    return True

def print_next_steps():
    """In ra các bước tiếp theo"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("="*60)
    print("\n📋 NEXT STEPS:")
    print("\n1. Configure API Keys:")
    print("   - Edit .env file with your exchange API keys")
    print("   - Set up Discord bot token")
    print("   - Configure Zalo API (optional)")
    
    print("\n2. Start Redis Server:")
    print("   - Windows: redis-server")
    print("   - Linux/macOS: sudo systemctl start redis")
    
    print("\n3. Test Configuration:")
    print("   - python -c \"from config.settings import settings; print('Config OK')\"")
    
    print("\n4. Run the Bot:")
    print("   - Development: python src/main.py")
    print("   - Production: python src/main.py --production")
    
    print("\n5. Run Tests:")
    print("   - pytest tests/ -v")
    
    print("\n6. Monitor Logs:")
    print("   - tail -f logs/trading_bot.log")
    
    print("\n📚 Documentation:")
    print("   - README.md - General information")
    print("   - PLAN.md - Implementation plan")
    print("   - TECHNICAL_SPECIFICATION.md - Technical details")
    
    print("\n⚠️  IMPORTANT REMINDERS:")
    print("   - Use testnet/sandbox APIs during development")
    print("   - Set API keys to READ-only permissions")
    print("   - Never commit .env file to version control")
    print("   - Monitor rate limits to avoid API restrictions")
    
    print("\n" + "="*60)

def main():
    """Main setup function"""
    print("🚀 Trading Signal Bot - Setup Script")
    print("="*50)
    
    # Run setup steps
    check_python_version()
    create_directories()
    setup_environment_file()
    install_requirements()
    check_redis()
    setup_git_hooks()
    create_sample_config()
    
    # Verify setup
    if run_initial_tests():
        print_next_steps()
    else:
        print("\n❌ Setup completed with errors. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
