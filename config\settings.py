"""
<PERSON><PERSON><PERSON> hình chính cho Trading Signal Bot
Quản lý tất cả các thiết lập hệ thống từ environment variables
"""

import os
from typing import List, Dict, Any
from dotenv import load_dotenv
from pydantic import BaseSettings, validator
import pytz

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Cấu hình chính của hệ thống"""
    
    # =============================================================================
    # APPLICATION SETTINGS
    # =============================================================================
    APP_NAME: str = "TradingSignalBot"
    APP_VERSION: str = "1.0.0"
    DEBUG_MODE: bool = True
    LOG_LEVEL: str = "INFO"
    TIMEZONE: str = "Asia/Ho_Chi_Minh"
    
    # =============================================================================
    # EXCHANGE API CONFIGURATION
    # =============================================================================
    # Binance
    BINANCE_API_KEY: str = ""
    BINANCE_SECRET_KEY: str = ""
    BINANCE_TESTNET: bool = True
    
    # MEXC
    MEXC_API_KEY: str = ""
    MEXC_SECRET_KEY: str = ""
    MEXC_TESTNET: bool = True
    
    # =============================================================================
    # NOTIFICATION SERVICES
    # =============================================================================
    # Discord
    DISCORD_BOT_TOKEN: str = ""
    DISCORD_CHANNEL_ID: str = ""
    DISCORD_GUILD_ID: str = ""
    
    # Zalo
    ZALO_APP_ID: str = ""
    ZALO_APP_SECRET: str = ""
    ZALO_ACCESS_TOKEN: str = ""
    ZALO_PHONE_NUMBER: str = ""
    
    # =============================================================================
    # DATABASE CONFIGURATION
    # =============================================================================
    DATABASE_URL: str = "sqlite:///data/trading_signals.db"
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""
    REDIS_DB: int = 0
    
    # =============================================================================
    # TRADING CONFIGURATION
    # =============================================================================
    MIN_CONFIDENCE_SCORE: int = 75
    MAX_SIGNALS_PER_HOUR: int = 10
    SIGNAL_VALIDITY_MINUTES: int = 240
    
    # Risk Management
    DEFAULT_RISK_PERCENTAGE: float = 2.0
    MAX_POSITION_SIZE_PERCENTAGE: float = 8.0
    MAX_TOTAL_EXPOSURE_PERCENTAGE: float = 25.0
    
    # Trading Pairs
    TRADING_PAIRS: str = "BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,SOL/USDT,MATIC/USDT,DOT/USDT,AVAX/USDT,LINK/USDT,UNI/USDT"
    TIMEFRAMES: str = "1m,5m,15m,1h,4h,1d"
    
    # =============================================================================
    # SYSTEM CONFIGURATION
    # =============================================================================
    API_RATE_LIMIT_PER_MINUTE: int = 1200
    WEBSOCKET_RECONNECT_DELAY: int = 5
    
    # Logging
    LOG_FILE_PATH: str = "logs/trading_bot.log"
    LOG_MAX_SIZE_MB: int = 100
    LOG_BACKUP_COUNT: int = 5
    
    # Monitoring
    ENABLE_PERFORMANCE_MONITORING: bool = True
    METRICS_PORT: int = 8000
    HEALTH_CHECK_INTERVAL_SECONDS: int = 60
    HEALTH_CHECK_PORT: int = 8001
    
    # =============================================================================
    # BACKTESTING CONFIGURATION
    # =============================================================================
    BACKTEST_START_DATE: str = "2023-01-01"
    BACKTEST_END_DATE: str = "2024-01-01"
    BACKTEST_INITIAL_BALANCE: float = 10000.0
    BACKTEST_COMMISSION_RATE: float = 0.001
    
    # =============================================================================
    # ADVANCED SETTINGS
    # =============================================================================
    # Market Structure Analysis
    ENABLE_MARKET_STRUCTURE: bool = True
    ENABLE_SMART_MONEY_CONCEPTS: bool = True
    ENABLE_VOLUME_PROFILE: bool = True
    
    # Crypto-Specific Features
    ENABLE_FUNDING_RATE_ANALYSIS: bool = True
    ENABLE_LONG_SHORT_RATIO: bool = True
    ENABLE_BTC_DOMINANCE_TRACKING: bool = True
    
    # Multi-timeframe Analysis
    ENABLE_MULTI_TIMEFRAME: bool = True
    REQUIRE_HTF_ALIGNMENT: bool = True
    MIN_TIMEFRAME_ALIGNMENT: int = 2
    
    # Signal Filtering
    ENABLE_NEWS_FILTER: bool = True
    ENABLE_VOLATILITY_FILTER: bool = True
    ENABLE_CORRELATION_FILTER: bool = True
    
    # =============================================================================
    # DEVELOPMENT & TESTING
    # =============================================================================
    ENABLE_PAPER_TRADING: bool = True
    ENABLE_SIGNAL_SIMULATION: bool = False
    MOCK_EXCHANGE_DATA: bool = False
    
    TEST_DATABASE_URL: str = "sqlite:///data/test_trading_signals.db"
    TEST_REDIS_DB: int = 1
    PYTEST_TIMEOUT: int = 30
    
    # =============================================================================
    # SECURITY SETTINGS
    # =============================================================================
    ENCRYPTION_KEY: str = ""
    HASH_SALT: str = ""
    ENABLE_API_KEY_ROTATION: bool = False
    API_KEY_ROTATION_DAYS: int = 30
    ALLOWED_IPS: str = "127.0.0.1,localhost"
    ENABLE_IP_WHITELIST: bool = False
    
    # =============================================================================
    # VALIDATORS
    # =============================================================================
    @validator('TRADING_PAIRS')
    def validate_trading_pairs(cls, v):
        """Validate và parse trading pairs"""
        pairs = [pair.strip().upper() for pair in v.split(',')]
        return pairs
    
    @validator('TIMEFRAMES')
    def validate_timeframes(cls, v):
        """Validate và parse timeframes"""
        valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
        timeframes = [tf.strip() for tf in v.split(',')]
        for tf in timeframes:
            if tf not in valid_timeframes:
                raise ValueError(f"Invalid timeframe: {tf}")
        return timeframes
    
    @validator('MIN_CONFIDENCE_SCORE')
    def validate_confidence_score(cls, v):
        """Validate confidence score range"""
        if not 0 <= v <= 100:
            raise ValueError("Confidence score must be between 0 and 100")
        return v
    
    @validator('TIMEZONE')
    def validate_timezone(cls, v):
        """Validate timezone"""
        try:
            pytz.timezone(v)
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(f"Invalid timezone: {v}")
        return v
    
    # =============================================================================
    # COMPUTED PROPERTIES
    # =============================================================================
    @property
    def timezone_obj(self):
        """Get timezone object"""
        return pytz.timezone(self.TIMEZONE)
    
    @property
    def redis_url(self) -> str:
        """Get Redis connection URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return not self.DEBUG_MODE
    
    @property
    def log_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
                },
                'detailed': {
                    'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
                }
            },
            'handlers': {
                'console': {
                    'level': self.LOG_LEVEL,
                    'class': 'logging.StreamHandler',
                    'formatter': 'standard'
                },
                'file': {
                    'level': self.LOG_LEVEL,
                    'class': 'logging.handlers.RotatingFileHandler',
                    'filename': self.LOG_FILE_PATH,
                    'maxBytes': self.LOG_MAX_SIZE_MB * 1024 * 1024,
                    'backupCount': self.LOG_BACKUP_COUNT,
                    'formatter': 'detailed'
                }
            },
            'loggers': {
                '': {
                    'handlers': ['console', 'file'],
                    'level': self.LOG_LEVEL,
                    'propagate': False
                }
            }
        }
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
settings = Settings()

# Export commonly used configurations
TRADING_PAIRS = settings.TRADING_PAIRS
TIMEFRAMES = settings.TIMEFRAMES
MIN_CONFIDENCE_SCORE = settings.MIN_CONFIDENCE_SCORE
DEBUG_MODE = settings.DEBUG_MODE
