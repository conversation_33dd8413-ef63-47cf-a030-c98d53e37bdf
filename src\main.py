#!/usr/bin/env python3
"""
Trading Signal Bot - Main Entry Point
Bot tự động phân tích kỹ thuật và gửi tín hiệu giao dịch cryptocurrency

Usage:
    python src/main.py [--production] [--config CONFIG_FILE]
"""

import asyncio
import argparse
import signal
import sys
import logging
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.settings import settings
from src.utils.logger import setup_logging
from src.utils.exceptions import TradingBotException

# Import main components (sẽ được implement trong các phase tiếp theo)
# from src.data.market_data import MarketDataManager
# from src.analysis.signal_engine import SignalEngine
# from src.notifications.alert_manager import AlertManager

class TradingSignalBot:
    """
    Main Trading Signal Bot class
    Quản lý toàn bộ lifecycle của bot
    """
    
    def __init__(self, production_mode: bool = False):
        """
        Khởi tạo Trading Signal Bot
        
        Args:
            production_mode: True nếu chạy ở production mode
        """
        self.production_mode = production_mode
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        # Components sẽ được khởi tạo trong setup()
        self.market_data_manager = None
        self.signal_engine = None
        self.alert_manager = None
        
        self.logger.info(f"Trading Signal Bot initialized - Mode: {'Production' if production_mode else 'Development'}")
    
    async def setup(self):
        """Thiết lập các components của bot"""
        try:
            self.logger.info("Setting up Trading Signal Bot components...")
            
            # TODO: Phase 1 - Setup data layer
            # self.market_data_manager = MarketDataManager()
            # await self.market_data_manager.initialize()
            
            # TODO: Phase 2 - Setup analysis engine
            # self.signal_engine = SignalEngine(self.market_data_manager)
            # await self.signal_engine.initialize()
            
            # TODO: Phase 3 - Setup notification system
            # self.alert_manager = AlertManager()
            # await self.alert_manager.initialize()
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to setup bot components: {e}")
            raise TradingBotException(f"Setup failed: {e}")
    
    async def start(self):
        """Khởi động bot"""
        try:
            self.logger.info("Starting Trading Signal Bot...")
            self.running = True
            
            # Setup components
            await self.setup()
            
            # Start main loop
            await self.run_main_loop()
            
        except KeyboardInterrupt:
            self.logger.info("Received shutdown signal")
            await self.shutdown()
        except Exception as e:
            self.logger.error(f"Bot crashed: {e}")
            await self.shutdown()
            raise
    
    async def run_main_loop(self):
        """Main loop của bot"""
        self.logger.info("Starting main loop...")
        
        while self.running:
            try:
                # TODO: Phase 1 - Implement main loop logic
                # 1. Collect market data
                # 2. Run analysis
                # 3. Generate signals
                # 4. Send notifications
                # 5. Update metrics
                
                # Placeholder - sleep for now
                await asyncio.sleep(1)
                
                # Log heartbeat every 60 seconds
                if hasattr(self, '_last_heartbeat'):
                    if (asyncio.get_event_loop().time() - self._last_heartbeat) >= 60:
                        self.logger.info("Bot is running - Heartbeat")
                        self._last_heartbeat = asyncio.get_event_loop().time()
                else:
                    self._last_heartbeat = asyncio.get_event_loop().time()
                
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                if self.production_mode:
                    # In production, continue running after errors
                    await asyncio.sleep(5)
                else:
                    # In development, stop on errors for debugging
                    raise
    
    async def shutdown(self):
        """Tắt bot một cách graceful"""
        self.logger.info("Shutting down Trading Signal Bot...")
        self.running = False
        
        try:
            # TODO: Cleanup components
            # if self.alert_manager:
            #     await self.alert_manager.shutdown()
            # if self.signal_engine:
            #     await self.signal_engine.shutdown()
            # if self.market_data_manager:
            #     await self.market_data_manager.shutdown()
            
            self.logger.info("Bot shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    def setup_signal_handlers(self):
        """Setup signal handlers cho graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Trading Signal Bot for Cryptocurrency')
    
    parser.add_argument(
        '--production',
        action='store_true',
        help='Run in production mode'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default=settings.LOG_LEVEL,
        help='Set logging level'
    )
    
    parser.add_argument(
        '--pairs',
        type=str,
        help='Comma-separated list of trading pairs to monitor'
    )
    
    parser.add_argument(
        '--timeframes',
        type=str,
        help='Comma-separated list of timeframes to analyze'
    )
    
    return parser.parse_args()

async def main():
    """Main function"""
    # Parse arguments
    args = parse_arguments()
    
    # Setup logging
    setup_logging(level=args.log_level)
    logger = logging.getLogger(__name__)
    
    # Log startup information
    logger.info("=" * 60)
    logger.info("TRADING SIGNAL BOT STARTING")
    logger.info("=" * 60)
    logger.info(f"Version: {settings.APP_VERSION}")
    logger.info(f"Mode: {'Production' if args.production else 'Development'}")
    logger.info(f"Debug: {settings.DEBUG_MODE}")
    logger.info(f"Timezone: {settings.TIMEZONE}")
    logger.info(f"Min Confidence Score: {settings.MIN_CONFIDENCE_SCORE}")
    logger.info(f"Trading Pairs: {len(settings.TRADING_PAIRS)} pairs")
    logger.info(f"Timeframes: {settings.TIMEFRAMES}")
    logger.info("=" * 60)
    
    try:
        # Create and start bot
        bot = TradingSignalBot(production_mode=args.production)
        bot.setup_signal_handlers()
        
        # Start bot
        await bot.start()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot failed to start: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        sys.exit(1)
    
    # Run main function
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
