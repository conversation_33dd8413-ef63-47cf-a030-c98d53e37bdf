# =============================================================================
# TRADING SIGNAL BOT - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env file to version control!

# =============================================================================
# EXCHANGE API CONFIGURATION
# =============================================================================

# Binance API (Primary Exchange)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true  # Set to false for live trading

# MEXC API (Secondary Exchange)
MEXC_API_KEY=your_mexc_api_key_here
MEXC_SECRET_KEY=your_mexc_secret_key_here
MEXC_TESTNET=true  # Set to false for live trading

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_CHANNEL_ID=your_discord_channel_id_here
DISCORD_GUILD_ID=your_discord_guild_id_here

# Zalo API Configuration
ZALO_APP_ID=your_zalo_app_id_here
ZALO_APP_SECRET=your_zalo_app_secret_here
ZALO_ACCESS_TOKEN=your_zalo_access_token_here
ZALO_PHONE_NUMBER=your_zalo_phone_number_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# SQLite Database (Default)
DATABASE_URL=sqlite:///data/trading_signals.db

# PostgreSQL (Production - Optional)
# DATABASE_URL=postgresql://username:password@localhost:5432/trading_signals

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Signal Generation Settings
MIN_CONFIDENCE_SCORE=75
MAX_SIGNALS_PER_HOUR=10
SIGNAL_VALIDITY_MINUTES=240

# Risk Management
DEFAULT_RISK_PERCENTAGE=2.0
MAX_POSITION_SIZE_PERCENTAGE=8.0
MAX_TOTAL_EXPOSURE_PERCENTAGE=25.0

# Supported Trading Pairs (comma-separated)
TRADING_PAIRS=BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,SOL/USDT,MATIC/USDT,DOT/USDT,AVAX/USDT,LINK/USDT,UNI/USDT

# Timeframes for Analysis (comma-separated)
TIMEFRAMES=1m,5m,15m,1h,4h,1d

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Application Settings
APP_NAME=TradingSignalBot
APP_VERSION=1.0.0
DEBUG_MODE=true
LOG_LEVEL=INFO

# Timezone
TIMEZONE=Asia/Ho_Chi_Minh

# Rate Limiting
API_RATE_LIMIT_PER_MINUTE=1200
WEBSOCKET_RECONNECT_DELAY=5

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log Configuration
LOG_FILE_PATH=logs/trading_bot.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
METRICS_PORT=8000

# Health Check
HEALTH_CHECK_INTERVAL_SECONDS=60
HEALTH_CHECK_PORT=8001

# =============================================================================
# BACKTESTING CONFIGURATION
# =============================================================================

# Backtesting Settings
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
BACKTEST_INITIAL_BALANCE=10000
BACKTEST_COMMISSION_RATE=0.001

# =============================================================================
# ADVANCED SETTINGS
# =============================================================================

# Market Structure Analysis
ENABLE_MARKET_STRUCTURE=true
ENABLE_SMART_MONEY_CONCEPTS=true
ENABLE_VOLUME_PROFILE=true

# Crypto-Specific Features
ENABLE_FUNDING_RATE_ANALYSIS=true
ENABLE_LONG_SHORT_RATIO=true
ENABLE_BTC_DOMINANCE_TRACKING=true

# Multi-timeframe Analysis
ENABLE_MULTI_TIMEFRAME=true
REQUIRE_HTF_ALIGNMENT=true
MIN_TIMEFRAME_ALIGNMENT=2

# Signal Filtering
ENABLE_NEWS_FILTER=true
ENABLE_VOLATILITY_FILTER=true
ENABLE_CORRELATION_FILTER=true

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Settings
ENABLE_PAPER_TRADING=true
ENABLE_SIGNAL_SIMULATION=false
MOCK_EXCHANGE_DATA=false

# Testing Configuration
TEST_DATABASE_URL=sqlite:///data/test_trading_signals.db
TEST_REDIS_DB=1
PYTEST_TIMEOUT=30

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key_here
HASH_SALT=your_hash_salt_here

# API Security
ENABLE_API_KEY_ROTATION=false
API_KEY_ROTATION_DAYS=30

# Access Control
ALLOWED_IPS=127.0.0.1,localhost
ENABLE_IP_WHITELIST=false

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================

# Telegram Bot (Optional)
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Email Notifications (Optional)
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
EMAIL_FROM=
EMAIL_TO=

# Webhook Notifications (Optional)
WEBHOOK_URL=
WEBHOOK_SECRET=

# =============================================================================
# NOTES
# =============================================================================
# 1. All API keys should have READ-ONLY permissions for safety
# 2. Use testnet/sandbox environments during development
# 3. Set DEBUG_MODE=false in production
# 4. Regularly rotate API keys and secrets
# 5. Monitor rate limits to avoid API restrictions
# 6. Keep this file secure and never share publicly
