"""
<PERSON><PERSON><PERSON> hình cho các sàn giao dịch cryptocurrency
Quản lý kết nối và thiết lập API cho Binance, MEXC và các sàn khác
"""

from typing import Dict, Any, List
import ccxt
from config.settings import settings

class ExchangeConfig:
    """C<PERSON>u hình cho các sàn giao dịch"""
    
    # =============================================================================
    # EXCHANGE CONFIGURATIONS
    # =============================================================================
    
    BINANCE_CONFIG = {
        'apiKey': settings.BINANCE_API_KEY,
        'secret': settings.BINANCE_SECRET_KEY,
        'sandbox': settings.BINANCE_TESTNET,
        'enableRateLimit': True,
        'rateLimit': 1200,  # requests per minute
        'options': {
            'defaultType': 'future',  # Use USDT-M futures
            'adjustForTimeDifference': True,
        },
        'urls': {
            'api': {
                'public': 'https://fapi.binance.com/fapi/v1',
                'private': 'https://fapi.binance.com/fapi/v1',
            } if not settings.BINANCE_TESTNET else {
                'public': 'https://testnet.binancefuture.com/fapi/v1',
                'private': 'https://testnet.binancefuture.com/fapi/v1',
            }
        }
    }
    
    MEXC_CONFIG = {
        'apiKey': settings.MEXC_API_KEY,
        'secret': settings.MEXC_SECRET_KEY,
        'sandbox': settings.MEXC_TESTNET,
        'enableRateLimit': True,
        'rateLimit': 1000,  # requests per minute
        'options': {
            'defaultType': 'swap',  # Use perpetual futures
        }
    }
    
    # =============================================================================
    # EXCHANGE FACTORY
    # =============================================================================
    
    @staticmethod
    def create_exchange(exchange_name: str) -> ccxt.Exchange:
        """
        Tạo instance của exchange
        
        Args:
            exchange_name: Tên sàn giao dịch ('binance', 'mexc')
            
        Returns:
            ccxt.Exchange: Instance của exchange
        """
        exchange_name = exchange_name.lower()
        
        if exchange_name == 'binance':
            return ccxt.binance(ExchangeConfig.BINANCE_CONFIG)
        elif exchange_name == 'mexc':
            return ccxt.mexc(ExchangeConfig.MEXC_CONFIG)
        else:
            raise ValueError(f"Unsupported exchange: {exchange_name}")
    
    @staticmethod
    def get_supported_exchanges() -> List[str]:
        """Lấy danh sách các sàn được hỗ trợ"""
        return ['binance', 'mexc']
    
    @staticmethod
    def get_primary_exchange() -> str:
        """Lấy sàn giao dịch chính"""
        return 'binance'
    
    @staticmethod
    def get_exchange_info(exchange_name: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về sàn giao dịch
        
        Args:
            exchange_name: Tên sàn giao dịch
            
        Returns:
            Dict: Thông tin về sàn giao dịch
        """
        exchange_info = {
            'binance': {
                'name': 'Binance',
                'type': 'futures',
                'fee_structure': {
                    'maker': 0.0002,  # 0.02%
                    'taker': 0.0004,  # 0.04%
                },
                'min_order_size': {
                    'BTC/USDT': 0.001,
                    'ETH/USDT': 0.001,
                    'default': 0.001
                },
                'rate_limits': {
                    'requests_per_minute': 1200,
                    'orders_per_second': 10,
                    'orders_per_day': 200000
                },
                'supported_timeframes': ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M'],
                'websocket_endpoints': {
                    'ticker': 'wss://fstream.binance.com/ws/!ticker@arr',
                    'kline': 'wss://fstream.binance.com/ws/{symbol}@kline_{interval}',
                    'depth': 'wss://fstream.binance.com/ws/{symbol}@depth@100ms'
                }
            },
            'mexc': {
                'name': 'MEXC',
                'type': 'swap',
                'fee_structure': {
                    'maker': 0.0002,  # 0.02%
                    'taker': 0.0006,  # 0.06%
                },
                'min_order_size': {
                    'BTC/USDT': 0.0001,
                    'ETH/USDT': 0.001,
                    'default': 0.001
                },
                'rate_limits': {
                    'requests_per_minute': 1000,
                    'orders_per_second': 5,
                    'orders_per_day': 100000
                },
                'supported_timeframes': ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M'],
                'websocket_endpoints': {
                    'ticker': 'wss://contract.mexc.com/ws',
                    'kline': 'wss://contract.mexc.com/ws',
                    'depth': 'wss://contract.mexc.com/ws'
                }
            }
        }
        
        return exchange_info.get(exchange_name.lower(), {})
    
    # =============================================================================
    # TRADING PAIRS CONFIGURATION
    # =============================================================================
    
    TIER_1_PAIRS = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT'
    ]
    
    TIER_2_PAIRS = [
        'ADA/USDT', 'SOL/USDT', 'MATIC/USDT', 'DOT/USDT',
        'AVAX/USDT', 'LINK/USDT', 'UNI/USDT'
    ]
    
    TIER_3_PAIRS = [
        'ATOM/USDT', 'FTM/USDT', 'NEAR/USDT', 'ALGO/USDT',
        'XRP/USDT', 'LTC/USDT', 'BCH/USDT', 'ETC/USDT'
    ]
    
    @staticmethod
    def get_trading_pairs_by_tier(tier: int = None) -> List[str]:
        """
        Lấy danh sách cặp giao dịch theo tier
        
        Args:
            tier: Tier của cặp giao dịch (1, 2, 3). None để lấy tất cả
            
        Returns:
            List[str]: Danh sách cặp giao dịch
        """
        if tier == 1:
            return ExchangeConfig.TIER_1_PAIRS
        elif tier == 2:
            return ExchangeConfig.TIER_2_PAIRS
        elif tier == 3:
            return ExchangeConfig.TIER_3_PAIRS
        else:
            return ExchangeConfig.TIER_1_PAIRS + ExchangeConfig.TIER_2_PAIRS + ExchangeConfig.TIER_3_PAIRS
    
    @staticmethod
    def get_pair_tier(symbol: str) -> int:
        """
        Xác định tier của cặp giao dịch
        
        Args:
            symbol: Symbol của cặp giao dịch
            
        Returns:
            int: Tier của cặp giao dịch (1, 2, 3)
        """
        if symbol in ExchangeConfig.TIER_1_PAIRS:
            return 1
        elif symbol in ExchangeConfig.TIER_2_PAIRS:
            return 2
        elif symbol in ExchangeConfig.TIER_3_PAIRS:
            return 3
        else:
            return 3  # Default to tier 3 for unknown pairs
    
    # =============================================================================
    # TIMEFRAME CONFIGURATION
    # =============================================================================
    
    TIMEFRAME_MAPPING = {
        '1m': 60,
        '3m': 180,
        '5m': 300,
        '15m': 900,
        '30m': 1800,
        '1h': 3600,
        '2h': 7200,
        '4h': 14400,
        '6h': 21600,
        '8h': 28800,
        '12h': 43200,
        '1d': 86400,
        '3d': 259200,
        '1w': 604800,
        '1M': 2592000
    }
    
    @staticmethod
    def get_timeframe_seconds(timeframe: str) -> int:
        """
        Chuyển đổi timeframe sang giây
        
        Args:
            timeframe: Timeframe string (e.g., '1h', '4h', '1d')
            
        Returns:
            int: Số giây tương ứng
        """
        return ExchangeConfig.TIMEFRAME_MAPPING.get(timeframe, 3600)
    
    @staticmethod
    def get_higher_timeframe(current_tf: str) -> str:
        """
        Lấy timeframe cao hơn cho multi-timeframe analysis
        
        Args:
            current_tf: Timeframe hiện tại
            
        Returns:
            str: Timeframe cao hơn
        """
        timeframes = list(ExchangeConfig.TIMEFRAME_MAPPING.keys())
        try:
            current_index = timeframes.index(current_tf)
            if current_index < len(timeframes) - 1:
                return timeframes[current_index + 1]
            return current_tf
        except ValueError:
            return '1h'  # Default fallback
    
    @staticmethod
    def get_lower_timeframe(current_tf: str) -> str:
        """
        Lấy timeframe thấp hơn cho entry timing
        
        Args:
            current_tf: Timeframe hiện tại
            
        Returns:
            str: Timeframe thấp hơn
        """
        timeframes = list(ExchangeConfig.TIMEFRAME_MAPPING.keys())
        try:
            current_index = timeframes.index(current_tf)
            if current_index > 0:
                return timeframes[current_index - 1]
            return current_tf
        except ValueError:
            return '5m'  # Default fallback

# Global exchange configuration instance
exchange_config = ExchangeConfig()
