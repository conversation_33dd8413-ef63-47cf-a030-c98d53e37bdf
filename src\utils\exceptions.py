"""
Custom exceptions cho Trading Signal Bot
Đ<PERSON>nh ngh<PERSON>a các exception classes cho error handling
"""

class TradingBotException(Exception):
    """Base exception class cho Trading Bot"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

class DataException(TradingBotException):
    """Exception cho data layer errors"""
    pass

class ExchangeException(DataException):
    """Exception cho exchange API errors"""
    
    def __init__(self, message: str, exchange: str = None, endpoint: str = None, **kwargs):
        self.exchange = exchange
        self.endpoint = endpoint
        super().__init__(message, **kwargs)

class RateLimitException(ExchangeException):
    """Exception cho rate limit errors"""
    
    def __init__(self, message: str, retry_after: int = None, **kwargs):
        self.retry_after = retry_after
        super().__init__(message, error_code="RATE_LIMIT", **kwargs)

class ConnectionException(ExchangeException):
    """Exception cho connection errors"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, error_code="CONNECTION_ERROR", **kwargs)

class AuthenticationException(ExchangeException):
    """Exception cho authentication errors"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, error_code="AUTH_ERROR", **kwargs)

class AnalysisException(TradingBotException):
    """Exception cho analysis engine errors"""
    pass

class IndicatorException(AnalysisException):
    """Exception cho technical indicator errors"""
    
    def __init__(self, message: str, indicator: str = None, **kwargs):
        self.indicator = indicator
        super().__init__(message, error_code="INDICATOR_ERROR", **kwargs)

class MarketStructureException(AnalysisException):
    """Exception cho market structure analysis errors"""
    
    def __init__(self, message: str, structure_type: str = None, **kwargs):
        self.structure_type = structure_type
        super().__init__(message, error_code="MARKET_STRUCTURE_ERROR", **kwargs)

class SignalException(TradingBotException):
    """Exception cho signal generation errors"""
    pass

class ConfidenceException(SignalException):
    """Exception cho confidence calculation errors"""
    
    def __init__(self, message: str, confidence_score: float = None, **kwargs):
        self.confidence_score = confidence_score
        super().__init__(message, error_code="CONFIDENCE_ERROR", **kwargs)

class ValidationException(SignalException):
    """Exception cho signal validation errors"""
    
    def __init__(self, message: str, validation_rule: str = None, **kwargs):
        self.validation_rule = validation_rule
        super().__init__(message, error_code="VALIDATION_ERROR", **kwargs)

class NotificationException(TradingBotException):
    """Exception cho notification system errors"""
    pass

class DiscordException(NotificationException):
    """Exception cho Discord notification errors"""
    
    def __init__(self, message: str, channel_id: str = None, **kwargs):
        self.channel_id = channel_id
        super().__init__(message, error_code="DISCORD_ERROR", **kwargs)

class ZaloException(NotificationException):
    """Exception cho Zalo notification errors"""
    
    def __init__(self, message: str, phone_number: str = None, **kwargs):
        self.phone_number = phone_number
        super().__init__(message, error_code="ZALO_ERROR", **kwargs)

class ConfigurationException(TradingBotException):
    """Exception cho configuration errors"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        self.config_key = config_key
        super().__init__(message, error_code="CONFIG_ERROR", **kwargs)

class DatabaseException(TradingBotException):
    """Exception cho database errors"""
    
    def __init__(self, message: str, operation: str = None, **kwargs):
        self.operation = operation
        super().__init__(message, error_code="DATABASE_ERROR", **kwargs)

class CacheException(TradingBotException):
    """Exception cho cache/Redis errors"""
    
    def __init__(self, message: str, cache_key: str = None, **kwargs):
        self.cache_key = cache_key
        super().__init__(message, error_code="CACHE_ERROR", **kwargs)

class BacktestException(TradingBotException):
    """Exception cho backtesting errors"""
    
    def __init__(self, message: str, backtest_period: str = None, **kwargs):
        self.backtest_period = backtest_period
        super().__init__(message, error_code="BACKTEST_ERROR", **kwargs)

# Utility functions for exception handling

def handle_exchange_error(error, exchange: str = None, endpoint: str = None):
    """
    Convert exchange errors to appropriate exceptions
    
    Args:
        error: Original error from exchange
        exchange: Exchange name
        endpoint: API endpoint
        
    Returns:
        TradingBotException: Appropriate exception type
    """
    error_message = str(error)
    
    if "rate limit" in error_message.lower():
        return RateLimitException(
            f"Rate limit exceeded: {error_message}",
            exchange=exchange,
            endpoint=endpoint
        )
    elif "authentication" in error_message.lower() or "api key" in error_message.lower():
        return AuthenticationException(
            f"Authentication failed: {error_message}",
            exchange=exchange,
            endpoint=endpoint
        )
    elif "connection" in error_message.lower() or "timeout" in error_message.lower():
        return ConnectionException(
            f"Connection error: {error_message}",
            exchange=exchange,
            endpoint=endpoint
        )
    else:
        return ExchangeException(
            f"Exchange error: {error_message}",
            exchange=exchange,
            endpoint=endpoint
        )

def log_exception(logger, exception: TradingBotException, context: dict = None):
    """
    Log exception với context information
    
    Args:
        logger: Logger instance
        exception: Exception to log
        context: Additional context information
    """
    context = context or {}
    
    log_data = {
        'error_type': type(exception).__name__,
        'error_code': exception.error_code,
        'message': exception.message,
        'details': exception.details,
        **context
    }
    
    logger.error(f"Exception occurred: {exception}", extra=log_data)
