# 🚀 Trading Signal Bot - Cryptocurrency

Bot tự động phân tích kỹ thuật và gửi tín hiệu giao dịch cryptocurrency với độ chính xác cao, tích hợp Market Structure Analysis và Smart Money Concepts.

## ✨ Tính năng chính

### 📊 Phân tích kỹ thuật tiên tiến
- **Market Structure Analysis**: BOS, CHoCH, Order Blocks, Fair Value Gaps
- **Smart Money Concepts**: Institutional flow, Volume Profile, Liquidity zones
- **Multi-timeframe Analysis**: Phân tích đa khung thời gian
- **Advanced Confidence Score**: Hệ thống tính điểm tin cậy 0-100 (minimum 75)
- **Market Regime Detection**: Tự động nhận diện trending/ranging/volatile markets

### 🔔 Thông báo real-time
- **Discord Integration**: Rich embed messages với charts và chi tiết đầy đủ
- **Zalo Integration**: Thông báo mobile nhanh chóng
- **Priority-based Routing**: Th<PERSON><PERSON> báo theo mức độ ưu tiên
- **Rate Limiting**: Tránh spam và tuân thủ API limits

### 💹 Quản lý rủi ro thông minh
- **Dynamic Position Sizing**: Tự động điều chỉnh theo confidence và volatility
- **Structure-based Stop Loss**: Stop loss dựa trên market structure
- **Multi-target Take Profit**: 3 mức take profit với tỷ lệ phân bổ
- **Correlation Analysis**: Tránh over-exposure trên các cặp tương quan cao

### 🏦 Hỗ trợ đa sàn giao dịch
- **Binance Futures**: Sàn chính với USDT-M futures
- **MEXC**: Sàn phụ với perpetual contracts
- **Real-time Data**: WebSocket connections cho dữ liệu real-time
- **Historical Data**: Lưu trữ và phân tích dữ liệu lịch sử

## 🛠️ Cài đặt

### Yêu cầu hệ thống
- Python 3.9 hoặc cao hơn
- Redis server (cho caching)
- 4GB RAM khuyến nghị
- Kết nối internet ổn định

### 1. Clone repository
```bash
git clone https://github.com/your-username/trading-signal-bot.git
cd trading-signal-bot
```

### 2. Tạo virtual environment
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

### 3. Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### 4. Cấu hình environment
```bash
# Copy file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env với thông tin của bạn
notepad .env  # Windows
nano .env     # Linux/macOS
```

### 5. Cài đặt Redis
```bash
# Windows (using Chocolatey)
choco install redis-64

# Ubuntu/Debian
sudo apt-get install redis-server

# macOS (using Homebrew)
brew install redis
```

### 6. Khởi động Redis
```bash
# Windows
redis-server

# Linux/macOS
sudo systemctl start redis
# hoặc
redis-server
```

## ⚙️ Cấu hình

### API Keys
1. **Binance API**:
   - Truy cập [Binance API Management](https://www.binance.com/en/my/settings/api-management)
   - Tạo API key với quyền "Read Only" và "Futures"
   - Thêm vào `.env`: `BINANCE_API_KEY` và `BINANCE_SECRET_KEY`

2. **MEXC API** (tùy chọn):
   - Truy cập [MEXC API Management](https://www.mexc.com/user/openapi)
   - Tạo API key với quyền "Read Only"
   - Thêm vào `.env`: `MEXC_API_KEY` và `MEXC_SECRET_KEY`

### Discord Bot
1. Tạo Discord Application tại [Discord Developer Portal](https://discord.com/developers/applications)
2. Tạo Bot và copy Bot Token
3. Invite bot vào server với quyền "Send Messages" và "Embed Links"
4. Thêm vào `.env`: `DISCORD_BOT_TOKEN`, `DISCORD_CHANNEL_ID`, `DISCORD_GUILD_ID`

### Zalo API (tùy chọn)
1. Đăng ký Zalo Official Account
2. Tạo Zalo Mini App tại [Zalo Developers](https://developers.zalo.me/)
3. Lấy App ID, App Secret và Access Token
4. Thêm vào `.env`: `ZALO_APP_ID`, `ZALO_APP_SECRET`, `ZALO_ACCESS_TOKEN`

## 🚀 Chạy Bot

### Development Mode
```bash
python src/main.py
```

### Production Mode
```bash
# Đặt DEBUG_MODE=false trong .env
python src/main.py --production
```

### Với Docker (sắp có)
```bash
docker-compose up -d
```

## 📊 Monitoring

### Health Check
Bot cung cấp health check endpoint:
```
http://localhost:8001/health
```

### Metrics
Prometheus metrics có sẵn tại:
```
http://localhost:8000/metrics
```

### Logs
Logs được lưu tại `logs/trading_bot.log` với rotation tự động.

## 🧪 Testing

### Chạy unit tests
```bash
pytest tests/ -v
```

### Chạy tests với coverage
```bash
pytest tests/ --cov=src --cov-report=html
```

### Backtesting
```bash
python backtest/backtester.py --start-date 2023-01-01 --end-date 2024-01-01
```

## 📈 Performance

### Benchmarks mục tiêu
- **Win Rate**: >65% (vs industry 50-60%)
- **Average R:R**: >2.5:1
- **Profit Factor**: >2.0
- **Maximum Drawdown**: <15%
- **Signal Latency**: <5 seconds
- **System Uptime**: >99.5%

### Optimization
- Sử dụng Redis cho caching
- Async programming cho performance
- Rate limiting để tránh API restrictions
- Memory optimization cho long-running processes

## 🔧 Troubleshooting

### Lỗi thường gặp

1. **API Connection Error**:
   ```
   Kiểm tra API keys và network connection
   Đảm bảo API keys có quyền phù hợp
   ```

2. **Redis Connection Error**:
   ```
   Khởi động Redis server
   Kiểm tra REDIS_HOST và REDIS_PORT trong .env
   ```

3. **Discord Bot Error**:
   ```
   Kiểm tra bot token và permissions
   Đảm bảo bot đã được invite vào server
   ```

4. **High Memory Usage**:
   ```
   Giảm số lượng trading pairs
   Tăng cache cleanup frequency
   ```

### Debug Mode
Bật debug mode trong `.env`:
```
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

## 📚 Documentation

- [API Documentation](docs/API_DOCUMENTATION.md)
- [Setup Guide](docs/SETUP_GUIDE.md)
- [User Manual](docs/USER_MANUAL.md)
- [Technical Specification](TECHNICAL_SPECIFICATION.md)
- [Implementation Plan](PLAN.md)

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## ⚠️ Disclaimer

**CẢNH BÁO**: Bot này chỉ cung cấp tín hiệu phân tích kỹ thuật và KHÔNG phải lời khuyên đầu tư. Giao dịch cryptocurrency có rủi ro cao và bạn có thể mất toàn bộ số tiền đầu tư. Luôn thực hiện nghiên cứu riêng và chỉ đầu tư số tiền bạn có thể chấp nhận mất.

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/trading-signal-bot/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/trading-signal-bot/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [CCXT](https://github.com/ccxt/ccxt) - Cryptocurrency exchange library
- [TA-Lib](https://github.com/mrjbq7/ta-lib) - Technical analysis library
- [Discord.py](https://github.com/Rapptz/discord.py) - Discord API wrapper
- [Pandas](https://pandas.pydata.org/) - Data analysis library

---

**Made with ❤️ for the crypto trading community**
