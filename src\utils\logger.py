"""
Logging utilities cho Trading Signal Bot
Cung cấp structured logging với rotation và multiple handlers
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
import structlog
from config.settings import settings

def setup_logging(level: str = "INFO", log_file: Optional[str] = None):
    """
    Thiết lập logging system cho bot
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR)
        log_file: Path to log file (optional)
    """
    
    # Create logs directory if not exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure log level
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create formatters
    console_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)8s] %(name)s:%(lineno)d: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    log_file_path = log_file or settings.LOG_FILE_PATH
    file_handler = logging.handlers.RotatingFileHandler(
        log_file_path,
        maxBytes=settings.LOG_MAX_SIZE_MB * 1024 * 1024,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Configure specific loggers
    configure_specific_loggers(log_level)
    
    # Setup structlog for structured logging
    setup_structlog()
    
    logging.info(f"Logging initialized - Level: {level}, File: {log_file_path}")

def configure_specific_loggers(log_level: int):
    """Cấu hình các logger cụ thể"""
    
    # Reduce noise from external libraries
    logging.getLogger('ccxt').setLevel(logging.WARNING)
    logging.getLogger('websocket').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('discord').setLevel(logging.INFO)
    
    # Set our loggers
    logging.getLogger('trading_bot').setLevel(log_level)
    logging.getLogger('market_data').setLevel(log_level)
    logging.getLogger('signal_engine').setLevel(log_level)
    logging.getLogger('notifications').setLevel(log_level)

def setup_structlog():
    """Setup structlog for structured logging"""
    
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

def get_logger(name: str) -> logging.Logger:
    """
    Lấy logger với tên cụ thể
    
    Args:
        name: Tên logger
        
    Returns:
        logging.Logger: Logger instance
    """
    return logging.getLogger(name)

def get_structured_logger(name: str):
    """
    Lấy structured logger
    
    Args:
        name: Tên logger
        
    Returns:
        structlog logger instance
    """
    return structlog.get_logger(name)

class TradingBotLogger:
    """Custom logger class cho Trading Bot với additional context"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.struct_logger = structlog.get_logger(name)
    
    def info(self, message: str, **kwargs):
        """Log info message với context"""
        self.logger.info(message)
        if kwargs:
            self.struct_logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message với context"""
        self.logger.warning(message)
        if kwargs:
            self.struct_logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message với context"""
        self.logger.error(message)
        if kwargs:
            self.struct_logger.error(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message với context"""
        self.logger.debug(message)
        if kwargs:
            self.struct_logger.debug(message, **kwargs)
    
    def signal(self, signal_data: dict):
        """Log trading signal với structured data"""
        self.logger.info(f"Signal generated: {signal_data.get('signal_type')} {signal_data.get('symbol')}")
        self.struct_logger.info(
            "trading_signal_generated",
            signal_type=signal_data.get('signal_type'),
            symbol=signal_data.get('symbol'),
            confidence=signal_data.get('confidence_score'),
            timeframe=signal_data.get('timeframe'),
            entry_price=signal_data.get('entry_price'),
            stop_loss=signal_data.get('stop_loss')
        )
    
    def performance(self, metric_name: str, value: float, **context):
        """Log performance metrics"""
        self.logger.info(f"Performance metric - {metric_name}: {value}")
        self.struct_logger.info(
            "performance_metric",
            metric=metric_name,
            value=value,
            **context
        )
    
    def api_call(self, exchange: str, endpoint: str, duration: float, success: bool = True):
        """Log API calls"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.debug(f"API call - {exchange} {endpoint}: {status} ({duration:.3f}s)")
        self.struct_logger.info(
            "api_call",
            exchange=exchange,
            endpoint=endpoint,
            duration=duration,
            success=success
        )

# Convenience function to get trading bot logger
def get_trading_logger(name: str) -> TradingBotLogger:
    """
    Lấy TradingBotLogger instance
    
    Args:
        name: Tên logger
        
    Returns:
        TradingBotLogger: Custom logger instance
    """
    return TradingBotLogger(name)
